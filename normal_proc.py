import logging
from queue import Empty
from proc import DataProcessor

logger = logging.getLogger('Normal Processor')

class Normal_Processor(DataProcessor):
    def __init__(self, com):
        super().__init__(com)
        self.display_hex = False
        self.send_hex = False
        self.show_timestamp = True

    def set_display_mode(self, use_hex):
        self.display_hex = use_hex

    def set_send_mode(self, use_hex):
        self.send_hex = use_hex

    def set_timestamp_mode(self, show):
        self.show_timestamp = show

    def process_queue(self):
        """从队列中取出完整帧和时间戳并处理"""
        frames = []
        raw_frames = []
        total_bytes = 0

        try:
            while True:
                frame, timestamp = self.data_queue.get_nowait()
                total_bytes += len(frame)

                raw_data = ' '.join(f'{byte:02X}' for byte in frame)
                raw_frames.append(f"[{timestamp}] {raw_data}")

                if self.display_hex:
                    processed_data = raw_data
                else:
                    try:
                        processed_data = frame.decode('ascii').rstrip()  # 移除残余换行符
                    except UnicodeDecodeError:
                        # processed_data = "(non-ASCII)"
                        processed_data = raw_data

                if self.show_timestamp:
                    processed_data = f"{timestamp} -> {processed_data}"
                frames.append(processed_data)

        except Empty:
            pass

        result = '\n'.join(raw_frames) if raw_frames else None
        parsed_result = '\n'.join(frames) if frames else None
        
        if result or parsed_result:
            return {
                'raw_data': result,
                'parsed_data': parsed_result,
                'bytes': total_bytes
            }
        return None

    def send(self, data):
        # 如果已经是字节数据（如HEX模式附加的结束符），直接发送
        if isinstance(data, bytes):
            self.com.send_data(data)
            return
        
        if isinstance(data, str):
            if self.send_hex:
                try:
                    hex_data = bytes.fromhex(data.replace(' ', ''))
                    self.com.send_data(hex_data)
                except ValueError:
                    raise ValueError("Invalid HEX format")
            else:
                self.com.send_data(data.encode('utf-8'))
        else:
            self.com.send_data(data)