import sys
import glob
import serial
import logging
from datetime import datetime
import serial.tools.list_ports
from PySide6.QtCore import Signal, QObject # type: ignore

logger = logging.getLogger('Com')

class SerialCom(QObject):
    port_reopened = Signal()
    
    def __init__(self):
        super().__init__()
        thread = self.thread()
        thread_name = thread.objectName() if thread else "None"
        logger.debug(f"SerialCom initialized, thread: {thread_name}, thread id: {id(thread) if thread else 'None'}")
        self.serial_port = None
        self.buffer = bytearray()
        self.separators = [b'\r\n', b'\n\r', b'\n', b'\r']
        self.is_cec_debug = False

    @staticmethod
    def get_available_ports():
        """获取可用串口列表（跨平台版本）"""
        # import platform
        # system = platform.system()
        
        # if system == "Windows":
        #     ports = [port.device for port in serial.tools.list_ports.comports()]
        # elif system == "Darwin":  # macOS
        #     ports = [port.device for port in serial.tools.list_ports.grep(r'usb|tty\.usb')]
        # elif system == "Linux":
        #     ports = [port.device for port in serial.tools.list_ports.grep(r'ttyUSB|ttyACM')]
        # else:
        #     ports = []
        
        # return ports

        if sys.platform.startswith('win'):
            ports = ['COM%s' % (i + 1) for i in range(256)]
        elif sys.platform.startswith('linux') or sys.platform.startswith('cygwin'):
            ports = glob.glob('/dev/tty[A-Za-z]*')
        elif sys.platform.startswith('darwin'):
            ports = glob.glob('/dev/tty.*')
        else:
            raise EnvironmentError('Unsupported platform')
        result = []
        for port in ports:
            try:
                s = serial.Serial(port)
                s.close()
                result.append(port)
            except (OSError, serial.SerialException):
                pass
        return result

    def is_port_open(self):
        return self.serial_port and self.serial_port.is_open

    def open_port(self, port, baudrate):
        try:
            self.serial_port = serial.Serial(port, baudrate, timeout=0.1)
            logger.info(f"Opened port {port} at {baudrate} baud")
            return True
        except Exception as e:
            logger.error(f"Error opening port: {e}")
            return False

    def close_port(self):
        if self.serial_port and self.serial_port.is_open:
            self.serial_port.close()
            logger.info("Port closed")

    def send_data(self, data):
        if self.serial_port and self.serial_port.is_open:
            self.serial_port.write(data)
            self.serial_port.flush()
            # logger.debug(f"Sent data: {data.hex()}")

    def receive_data(self):
        """接收原始数据并返回(数据, 时间戳)"""
        if not self.serial_port or not self.serial_port.is_open:
            logger.debug("Port not open or not initialized")
            return None

        try:
            if self.serial_port.in_waiting:
                raw_bytes = self.serial_port.read(self.serial_port.in_waiting)
                self.buffer.extend(raw_bytes)
        except serial.SerialException as e:
            logger.error(f"Serial error while reading: {e}")
            port = self.serial_port.port if self.serial_port else None
            baudrate = self.serial_port.baudrate if self.serial_port else None
            reopened = False
            try:
                self.close_port()
                if port and baudrate and self.open_port(port, baudrate):
                    logger.info("Successfully reopened serial port after error")
                    reopened = True
                else:
                    logger.error("Failed to reopen serial port: Invalid port or baudrate")
                    return None
            except Exception as reopen_e:
                logger.error(f"Error during reopen attempt: {reopen_e}")
                return None

            if reopened:
                # 直接发射信号，不使用 invokeMethod，由主线程处理
                try:
                    thread = self.thread()
                    thread_name = thread.objectName() if thread else "None"
                    logger.debug(f"Emitting port_reopened signal, thread: {thread_name}, thread id: {id(thread) if thread else 'None'}")
                    self.port_reopened.emit()
                except RuntimeError as emit_error:
                    logger.error(f"Failed to emit port_reopened signal: {emit_error}")

            return None

        if not self.buffer:
            return None

        timestamp = datetime.now().strftime('%H:%M:%S.%f')[:-3]

        if self.is_cec_debug:
            frame = self.buffer[:]
            self.buffer.clear()  # 清空缓冲区，交给 cec_proc 处理
            return frame, timestamp

        else:
            for sep in self.separators:
                if sep in self.buffer:
                    pos = self.buffer.find(sep)
                    frame = self.buffer[:pos]
                    self.buffer = self.buffer[pos + len(sep):]
                    if frame:
                        return frame, timestamp
                    break
            # 如果没有分隔符，返回当前缓冲区所有数据并清空
            frame = self.buffer[:]
            self.buffer.clear()
            if frame:
                return frame, timestamp
            return None