import os
import logging
import platform
from io import StringIO
from PySide6.QtWidgets import QApplication # type: ignore
from config import APP_NAME, AUTHOR, VERSION
from PySide6.QtGui import QFontDatabase, QFont # type: ignore

class StyleLoader:
    def __init__(self, resource_path_func, qss_dir=".", font_dir="fonts"):
        """
        初始化样式加载器
        :param resource_path_func: 用于获取资源路径的函数
        :param qss_dir: QSS 文件目录
        :param font_dir: 字体文件目录
        """
        self.resource_path = resource_path_func
        self.qss_dir = qss_dir
        self.font_dir = font_dir
        self.windows_version = self._get_windows_version()

        # 初始化临时日志
        self.log_stream = StringIO()
        self.temp_handler = logging.StreamHandler(self.log_stream)
        self.temp_handler.setFormatter(logging.Formatter('%(asctime)s - %(levelname)s - [%(name)s] - %(message)s'))
        self.logger = logging.getLogger()
        self.logger.setLevel(logging.DEBUG)
        self.logger.addHandler(self.temp_handler)

    @staticmethod
    def _get_windows_version():
        """获取 Windows 版本"""
        system = platform.system()
        if system != "Windows":
            return None
        version = platform.release()
        if version == "10":
            build = platform.version().split('.')[-1]
            if int(build) >= 22000:
                return "11"
            return "10"
        return version

    def load_fonts(self, app: QApplication):
        """加载并全局应用 TTF 字体"""
        font_file = self.resource_path(os.path.join(self.font_dir, "JetBrainsMono-Bold.ttf"))
        font_filename = os.path.splitext(os.path.basename(font_file))[0]
        if os.path.exists(font_file):
            font_id = QFontDatabase.addApplicationFont(font_file)
            if font_id != -1:
                font_family = QFontDatabase.applicationFontFamilies(font_id)[0]
                custom_font = QFont(font_family, 9)
                app.setFont(custom_font)
                self.logger.info(f"Loaded and applied font: {font_family}")
            else:
                app.setFont(QFont("Consolas", 9))
                self.logger.error(f"Failed to load font: {font_filename}")
        else:
            app.setFont(QFont("Consolas", 9))
            self.logger.warning(f"Failed to find font: {font_filename}")

    def load_qss(self, app: QApplication):
        """加载 QSS 样式根据 Windows 版本"""
        if not self.windows_version:
            qss_file = self.resource_path(os.path.join(self.qss_dir, "style.qss"))
        else:
            qss_file = self.resource_path(os.path.join(self.qss_dir, f"style_win{self.windows_version}.qss"))
        qss_filename = os.path.splitext(os.path.basename(qss_file))[0]
        try:
            with open(qss_file, "r", encoding="utf-8") as f:
                app.setStyleSheet(f.read())
            self.logger.info(f"Loaded and applied qss: {qss_filename}")
        except FileNotFoundError:
            self.logger.error(f"Failed to find qss: {qss_filename}")
        except UnicodeDecodeError as e:
            self.logger.error(f"Failed to decode qss {qss_filename}: {e}")

    def apply_styles(self, app: QApplication):
        """应用所有样式(QSS 和字体)并记录初始日志"""
        self.logger.info(f"{APP_NAME} started, v{VERSION} by <u>{AUTHOR}</u>")
        self.logger.info(f"System Info: <u>{platform.system()} {platform.release()}, {platform.machine()}, {platform.version()}</u>")
        self.load_fonts(app)
        self.load_qss(app)

    def transfer_logs(self, gui):
        """将临时日志转移到 GUI 的 debug_text"""
        from controller import QTextEditHandler

        # 清除临时处理器，设置 GUI 日志处理器
        self.logger.handlers.clear()
        handler = QTextEditHandler(gui)
        handler.setFormatter(logging.Formatter('%(asctime)s - %(levelname)s - [%(name)s] - %(message)s'))
        self.logger.addHandler(handler)

        # 转移临时日志
        self.log_stream.seek(0)
        for line in self.log_stream.readlines():
            gui.append_debug(line.strip())
        self.log_stream.close()