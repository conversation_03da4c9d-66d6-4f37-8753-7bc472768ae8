#!/usr/bin/env python3
"""
简单的Slint测试脚本
"""

import sys
import logging

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger('Test')

def test_imports():
    """测试导入"""
    try:
        import slint
        logger.info("✓ Slint imported successfully")
        
        from gui import SlintGUI
        logger.info("✓ SlintGUI imported successfully")
        
        from com import SerialCom
        logger.info("✓ SerialCom imported successfully")
        
        from controller import SerialController
        logger.info("✓ SerialController imported successfully")
        
        return True
    except Exception as e:
        logger.error(f"✗ Import failed: {e}")
        return False

def test_gui_creation():
    """测试GUI创建"""
    try:
        from gui import SlintGUI
        gui = SlintGUI()
        logger.info("✓ SlintGUI created successfully")
        return True
    except Exception as e:
        logger.error(f"✗ GUI creation failed: {e}")
        return False

def test_com_creation():
    """测试串口通信创建"""
    try:
        from com import SerialCom
        com = SerialCom()
        logger.info("✓ SerialCom created successfully")
        
        # 测试获取端口列表
        ports = com.get_available_ports()
        logger.info(f"✓ Available ports: {ports}")
        return True
    except Exception as e:
        logger.error(f"✗ SerialCom creation failed: {e}")
        return False

def test_controller_creation():
    """测试控制器创建"""
    try:
        from gui import SlintGUI
        from com import SerialCom
        from controller import SerialController
        
        gui = SlintGUI()
        com = SerialCom()
        controller = SerialController(com, gui)
        logger.info("✓ SerialController created successfully")
        return True
    except Exception as e:
        logger.error(f"✗ SerialController creation failed: {e}")
        return False

def main():
    """主测试函数"""
    logger.info("Starting Slint migration tests...")
    
    tests = [
        ("Import Test", test_imports),
        ("GUI Creation Test", test_gui_creation),
        ("SerialCom Creation Test", test_com_creation),
        ("Controller Creation Test", test_controller_creation),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        logger.info(f"\n--- Running {test_name} ---")
        if test_func():
            passed += 1
        else:
            logger.error(f"{test_name} failed!")
    
    logger.info(f"\n--- Test Results ---")
    logger.info(f"Passed: {passed}/{total}")
    
    if passed == total:
        logger.info("✓ All tests passed!")
        return 0
    else:
        logger.error("✗ Some tests failed!")
        return 1

if __name__ == "__main__":
    sys.exit(main())
