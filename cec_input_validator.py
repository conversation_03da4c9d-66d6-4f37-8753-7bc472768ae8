import logging
from PySide6.QtCore import QObject # type: ignore
from cec_definitions import CEC_OPCODES, OpcodeInfo, OPCODE_PARAMETER_LENGTHS

logger = logging.getLogger('Validator')

class CECInputValidator(QObject):
    def __init__(self, send_entry, opcode_combo=None):
        super().__init__()
        self.send_entry = send_entry
        self.opcode_combo = opcode_combo
        self.is_cec_debug = False
        self.is_hex_mode = False

    def set_cec_debug(self, enabled):
        self.is_cec_debug = enabled

    def set_hex_mode(self, enabled):
        self.is_hex_mode = enabled

    def validate_input(self, text):
        if not text:
            return text

        if self.is_cec_debug or self.is_hex_mode:
            cleaned_text = ''.join(c for c in text.upper() if c in "0123456789ABCDEF")
            if self.is_cec_debug:
                max_params = self.get_max_params()
                if len(cleaned_text) > max_params * 2:
                    cleaned_text = cleaned_text[:max_params * 2]
            return ' '.join(cleaned_text[i:i + 2] for i in range(0, len(cleaned_text), 2))
        
        return text

    def get_max_params(self):
        if not self.opcode_combo:
            return 0
        selected_opcode_desc = self.opcode_combo.currentText()
        opcode = next(key for key, value in CEC_OPCODES.items() if value == selected_opcode_desc)
        opcode_info = OPCODE_PARAMETER_LENGTHS.get(opcode, OpcodeInfo(0, True, None, None))
        if opcode_info.fixed and opcode_info.length is not None:
            return opcode_info.length
        elif opcode_info.fixed and opcode_info.lengths:
            return max(opcode_info.lengths)
        elif not opcode_info.fixed and opcode_info.length_range:
            return opcode_info.length_range[1]
        return 0

    def get_param_hint(self):
        """根据当前 opcode 生成参数长度提示"""
        if not self.opcode_combo:
            return "Enter hex data"
        
        selected_opcode_desc = self.opcode_combo.currentText()
        opcode = next(key for key, value in CEC_OPCODES.items() if value == selected_opcode_desc)
        opcode_info = OPCODE_PARAMETER_LENGTHS.get(opcode, OpcodeInfo(0, True, None, None))
        if opcode_info.length == 0 and opcode_info.fixed:
            return "No parameters needed"
        elif opcode_info.fixed and opcode_info.length is not None:
            return f"Enter hex data ({opcode_info.length} params)"
        elif opcode_info.fixed and opcode_info.lengths:
            return f"Enter hex data ({' or '.join(map(str, opcode_info.lengths))} params)"
        elif not opcode_info.fixed and opcode_info.length_range:
            min_len, max_len = opcode_info.length_range
            return f"Enter hex data ({min_len}-{max_len} params)"
        return "No parameters needed"

    @staticmethod
    def check_params_length(data, opcode):
        """检查输入参数长度是否符合当前 opcode 的要求"""
        if not data:
            param_length = 0
            params = b''
        else:
            try:
                params = bytes.fromhex(data.replace(' ', ''))
                param_length = len(params)
            except ValueError as e:
                logger.error(f"Invalid HEX input: {str(e)}")
                return False, None

        opcode_info = OPCODE_PARAMETER_LENGTHS.get(opcode, OpcodeInfo(0, True, None, None))
        
        if opcode_info.fixed and opcode_info.length is not None:
            if param_length != opcode_info.length:
                logger.error(f"Invalid parameter length: expected {opcode_info.length} byte(s), got {param_length}")
                return False, None
            return True, params
        elif opcode_info.fixed and opcode_info.lengths:
            if param_length not in opcode_info.lengths:
                logger.error(f"Invalid parameter length: expected {opcode_info.lengths} byte(s), got {param_length}")
                return False, None
            return True, params
        elif not opcode_info.fixed and opcode_info.length_range:
            min_len, max_len = opcode_info.length_range
            if not (min_len <= param_length <= max_len):
                logger.error(f"Invalid parameter length: expected {min_len}-{max_len} byte(s), got {param_length}")
                return False, None
            return True, params
        return True, params  # 默认允许任何长度

    def connect(self):
        self.send_entry.textChanged.connect(self.on_text_changed)

    def on_text_changed(self, text):
        validated_text = self.validate_input(text)
        if validated_text != text:
            # cursor_pos = self.send_entry.cursorPosition()
            self.send_entry.setText(validated_text)
            # self.send_entry.setCursorPosition(min(cursor_pos, len(validated_text)))