import logging
from queue import Empty
from proc import DataProcessor
from cec_parser import CECParser
from cec_definitions import OPCODE_PARAMETER_LENGTHS

logger = logging.getLogger('CEC Processor')

class CEC_Processor(DataProcessor):
    @staticmethod
    def process_frame(frame, timestamp):
        """解析并验证 CEC 数据帧"""
        if len(frame) < 3:
            logger.debug(f"Partial CEC data, discarding: {' '.join(f'{byte:02X}' for byte in frame)}")
            return None

        if not frame.startswith(b'\xAA\x88'):
            logger.warning(f"Invalid CEC header, discarding: {' '.join(f'{byte:02X}' for byte in frame)}")
            return None

        length = frame[2]
        total_length = 2 + 1 + length  # 头(2) + 长度(1) + 数据和校验(length)

        if len(frame) < total_length:
            logger.debug(f"Waiting for complete CEC packet, expected length: {total_length}, current length: {len(frame)}, discarding: {' '.join(f'{byte:02X}' for byte in frame)}")
            return None

        if len(frame) > total_length:
            logger.warning(f"Extra data in CEC packet, expected length: {total_length}, current length: {len(frame)}, discarding: {' '.join(f'{byte:02X}' for byte in frame)}")
            #frame = frame[:total_length]
            return None

        checksum_received = frame[-1]
        checksum_calculated = sum(frame[:-1]) % 256

        if checksum_calculated != checksum_received:
            logger.warning(f"Invalid CEC packet: checksum mismatch, calculated {checksum_calculated:02X}, received {checksum_received:02X}, discarding: {' '.join(f'{byte:02X}' for byte in frame)}")
            return None

        # 校验和验证通过后，验证操作码和参数长度
        cec_data = frame[3:-2]  # 跳过协议头(AA 88 Len)和状态, 校验和
        
        if len(cec_data) > 1:  # 确保有操作码
            opcode = cec_data[1]
            opcode_info = OPCODE_PARAMETER_LENGTHS.get(opcode)
            
            if opcode_info:
                param_length = len(cec_data) - 2  # 减去头和操作码
                
                # 处理固定长度的情况
                if opcode_info.fixed and opcode_info.length is not None:
                    if param_length != opcode_info.length:
                        logger.warning(f"Invalid parameter length for opcode {opcode:02X}, expected {opcode_info.length}, got {param_length}, discarding: {' '.join(f'{byte:02X}' for byte in frame)}")
                        return None
                
                # 处理特定长度列表的情况
                elif opcode_info.fixed and opcode_info.lengths:
                    if param_length not in opcode_info.lengths:
                        logger.warning(f"Invalid parameter length for opcode {opcode:02X}, expected one of {opcode_info.lengths}, got {param_length}, discarding: {' '.join(f'{byte:02X}' for byte in frame)}")
                        return None
                
                # 处理长度范围的情况
                elif not opcode_info.fixed and opcode_info.length_range:
                    min_len, max_len = opcode_info.length_range
                    if param_length < min_len or param_length > max_len:
                        logger.warning(f"Invalid parameter length for opcode {opcode:02X}, expected between {min_len} and {max_len}, got {param_length}, discarding: {' '.join(f'{byte:02X}' for byte in frame)}")
                        return None

        # logger.info(f"Valid CEC frame processed: {' '.join(f'{byte:02X}' for byte in frame)}, timestamp: {timestamp}")
        return f"{timestamp} -> {' '.join(f'{byte:02X}' for byte in frame)}"

    def process_queue(self):
        """从队列中取出完整帧和时间戳并处理"""
        frames = []
        parsed_frames = []
        total_bytes = 0

        try:
            while True:
                frame, timestamp = self.data_queue.get_nowait()
                # logger.debug(f"Processing CEC frame from queue: {frame.hex()}, timestamp: {timestamp}")
                total_bytes += len(frame)
                result = self.process_frame(frame, timestamp)
                if result:
                    frames.append(result)

                    # CEC解析
                    parsed = CECParser.parse_frame(frame)
                    if parsed:
                        parsed_frames.append(f"{timestamp}\t{parsed['summary']}")

        except Empty:
            pass

        result = '\n'.join(frames) if frames else None
        parsed_result = '\n'.join(parsed_frames) if parsed_frames else None
        
        if result or parsed_result:
            return {
                'raw_data': result,
                'parsed_data': parsed_result,
                'bytes': total_bytes
            }
        return None

    def send(self, data):
        if isinstance(data, str):
            try:
                # 将输入字符串转换为字节
                params = bytes.fromhex(data.replace(' ', ''))
                
                # 构建CEC协议帧: 头(AA 88) + 长度 + 数据 + 状态 + 校验和
                length = len(params) + 2  # 数据长度 + 状态长度 + 校验和长度
                frame = b'\xAA\x88' + bytes([length]) + params + b'\x01'
                checksum = sum(frame) % 256
                frame += bytes([checksum])
                
                self.com.send_data(frame)
                # logger.debug(f"Sent CEC data: {' '.join(f'{byte:02X}' for byte in frame)}")
            except ValueError:
                logger.error("Invalid HEX format for CEC")
                raise ValueError("Invalid HEX format for CEC")
        else:
            self.com.send_data(data)
            # logger.debug(f"Sent raw data: {' '.join(f'{byte:02X}' for byte in data)}")