/* 主窗口背景色 */
QWidget {
    background-color: #24262B;
    border: 1px solid #27292D;
    /* border-radius: 5px; */
}

/* 每排面板样式 */
#panel {
    background-color: #181A1F;
    border: 1px solid #27292D;
    border-radius: 5px;
}

/* TitleBar 样式 */
QFrame#titleBar {
    background-color: #24262B;  /* 与主窗口一致，或稍作区分 */
    border: none;  /* 与其他控件边框一致 */
    border-radius: 5px;
}

/* TitleBar 中的 QLabel 样式（图标和标题文本） */
QFrame#titleBar QLabel {
    color: #999999;             /* 标题文字颜色 */
    font-weight: bold;
    border: none;
    background-color: transparent;  /* 避免背景色覆盖 */
}

/* TitleBar 中的按钮通用样式 */
QFrame#titleBar QPushButton {
    background-color: transparent;
    color: #999999;
    border: none;
    font-weight: bold;
    border-radius: 5px;
    min-width: 30px;
    min-height: 30px;
}

/* 最小化按钮悬停样式 */
QFrame#titleBar QPushButton#minimizeButton:hover {
    background-color: #343638;
    color: white;
}

/* 最大化/还原按钮悬停样式 */
QFrame#titleBar QPushButton#maximizeButton:hover {
    background-color: #343638;
    color: white;
}

/* 关闭按钮悬停样式 */
QFrame#titleBar QPushButton#closeButton:hover {
    background-color: #E81123;
    color: white;
}

QFrame#titleBar QLabel#titleLabel {
    background-color: #24262B;
    color: #999999;
    font-weight: bold;
    border: none;
}

QFrame#titleBar QLabel#titleIcon {
    background-color: #24262B;
    border: none;
}

/* QLable 样式 */
QLabel {
    background-color: #181A1F;
    color: #999999;
    font-weight: bold;
    border: none;
}


QLabel#connectStatusLabel {
/*    color: #FF5555; *//* 红色，未连接 */
    color: red;
}
QLabel#connectStatusLabel[connected="true"] {
/*    color: #55FF55; *//* 绿色，连接 */
    color: green;
}


/* QLineEdit 样式 */
QLineEdit {
    background-color: #181A1F;
    color: #999999;
    border: 1px solid #27292D;
    padding: 4px;
    border-radius: 5px;
}

/* QTextEdit 样式 */
QTextEdit {
    color: #999999;
    background-color: #181A1F;
    border: 1px solid #27292D;
    border-radius: 5px;
    padding: 2px;
}

QTextEdit QScrollBar:vertical {
    border: none;
    background: #181A1F; /* 滚动条背景色，与 panel 一致 */
    width: 6px; /* 滚动条宽度 */
    margin: 0px 0px 0px 0px;
    border-radius: 3px; /* 圆润边缘 */
}

QTextEdit QScrollBar::handle:vertical {
    background: #444444; /* 滑块颜色 */
    min-height: 30px; /* 滑块最小高度 */
    border-radius: 3px; /* 圆润滑块 */
}

QTextEdit QScrollBar::handle:vertical:hover {
    background: #444444; /* 滑块悬停时颜色 */
    width: 6px;
}

QTextEdit QScrollBar::add-line:vertical {
    height: 0px; /* 隐藏向下箭头 */
    subcontrol-position: bottom;
    subcontrol-origin: margin;
}

QTextEdit QScrollBar::sub-line:vertical {
    height: 0px; /* 隐藏向上箭头 */
    subcontrol-position: top;
    subcontrol-origin: margin;
}

QTextEdit QScrollBar::add-page:vertical, QTextEdit QScrollBar::sub-page:vertical {
    background: none; /* 移除背景区域的颜色 */
}

QTextEdit#recvText {
    font-family: "Consolas";
    font-size: 9pt;
}

/* QPushButton 样式 */
#conButton {
    background-color: #181A1F;
    color: #999999;
    border: 1px solid #27292D;
    padding: 5px 15px;
    border-radius: 5px;
}
#conButton:hover {
    background-color: #27292D;
}
#conButton:pressed {
    background-color: #333539;
}
/* ================ */
#savButton {
    background-color: #181A1F;
    color: #999999;
    border: 1px solid #27292D;
    padding: 5px 15px;
    border-radius: 5px;
}
#savButton:hover {
    background-color: #27292D;
}
#savButton:pressed {
    background-color: #333539;
}
/* ================ */
#clrRxButton {
    background-color: #181A1F;
    color: #999999;
    border: 1px solid #27292D;
    padding: 5px 15px;
    border-radius: 5px;
}
#clrRxButton:hover {
    background-color: #27292D;
}
#clrRxButton:pressed {
    background-color: #333539;
}
/* ================ */
#sndButton {
    background-color: #181A1F;
    color: #999999;
    border: 1px solid #27292D;
    padding: 5px 15px;
    border-radius: 5px;
}
#sndButton:hover {
    background-color: #27292D;
}
#sndButton:pressed {
    background-color: #333539;
}
/* ================ */
#clrTxButton {
    background-color: #181A1F;
    color: #999999;
    border: 1px solid #27292D;
    padding: 5px 15px;
    border-radius: 5px;
}
#clrTxButton:hover {
    background-color: #27292D;
}
#clrTxButton:pressed {
    background-color: #333539;
}

/* portCombo 样式 */
#portCombo {
    background-color: #181A1F;
    color: #999999;
    border: 1px solid #27292D;
    padding: 5px 20px 5px 20px;
    border-radius: 5px;
    text-align: center;
}
#portCombo:hover {
    background-color: #24262B;
}
#portCombo::drop-down {
    border: none;
    width: 20px;
}
#portCombo QAbstractItemView {
    background-color: #181A1F;
    color: #999999;
    border: none;
    padding: 5px;
    selection-background-color: #24262B;
    text-align: center;
}
#portCombo QAbstractItemView::item {
    height: 25px;
    border: 1px solid #181A1F;
}
#baudCombo QAbstractItemView::item:hover {
    background-color: #181A1F;
}
#portCombo QScrollBar:vertical {
    background: #181A1F;
    width: 10px;
    margin: 0px;
}
#portCombo QScrollBar::handle:vertical {
    background: #999999;
    border-radius: 5px;
    min-height: 20px;
}
#portCombo QScrollBar::add-line:vertical {
    height: 0px;
}
#portCombo QScrollBar::sub-line:vertical {
    height: 0px;
}
#portCombo QScrollBar::add-page:vertical, QScrollBar::sub-page:vertical {
    background: none;
}

/* baudCombo 样式 */
#baudCombo {
    background-color: #181A1F;
    color: #999999;
    border: 1px solid #27292D;
    padding: 5px 20px 5px 20px;
    border-radius: 5px;
    text-align: center;
}
#baudCombo:hover {
    background-color: #24262B;
}
#baudCombo::drop-down {
    border: none;
    width: 20px;
}
#baudCombo QAbstractItemView {
    background-color: #181A1F;
    color: #999999;
    border: none;
    padding: 5px;
    selection-background-color: #24262B;
    text-align: center;
}
#baudCombo QAbstractItemView::item {
    height: 25px;
    border: 1px solid #181A1F;
}
#baudCombo QAbstractItemView::item:hover {
    background-color: #181A1F;
}
#baudCombo QScrollBar:vertical {
    background: #181A1F;
    width: 10px;
    margin: 0px;
}
#baudCombo QScrollBar::handle:vertical {
    background: #999999;
    border-radius: 5px;
    min-height: 20px;
}
#baudCombo QScrollBar::add-line:vertical {
    height: 0px;
}
#baudCombo QScrollBar::sub-line:vertical {
    height: 0px;
}
#baudCombo QScrollBar::add-page:vertical, QScrollBar::sub-page:vertical {
    background: none;
}

/* sendCombo 样式 */
#sendCombo {
    background-color: #181A1F;
    color: #999999;
    border: 1px solid #27292D;
    padding: 5px 20px 5px 20px;
    border-radius: 5px;
    text-align: center;
}
#sendCombo:hover {
    background-color: #24262B;
}
#sendCombo::drop-down {
    border: none;
    width: 20px;
}
#sendCombo QAbstractItemView {
    background-color: #181A1F;
    color: #999999;
    border: none;
    padding: 5px;
    selection-background-color: #24262B;
    text-align: center;
}
#sendCombo QAbstractItemView::item {
    height: 25px;
    border: 1px solid #181A1F;
}
#baudCombo QAbstractItemView::item:hover {
    background-color: #181A1F;
}
#sendCombo QScrollBar:vertical {
    background: #181A1F;
    width: 0px;
    margin: 0px;
}
#sendCombo QScrollBar::handle:vertical {
    background: #999999;
    border-radius: 5px;
    min-height: 20px;
}
#sendCombo QScrollBar::add-line:vertical {
    height: 0px;
}
#sendCombo QScrollBar::sub-line:vertical {
    height: 0px;
}
#sendCombo QScrollBar::add-page:vertical, QScrollBar::sub-page:vertical {
    background: none;
}

/* initiatorCombo 样式 */
#initiatorCombo {
    background-color: #181A1F;
    color: #999999;
    border: 1px solid #27292D;
    padding: 5px 20px 5px 20px;
    border-radius: 5px;
    text-align: center;
}
#initiatorCombo:hover {
    background-color: #24262B;
}
#initiatorCombo::drop-down {
    border: none;
    width: 20px;
}
#initiatorCombo QAbstractItemView {
    background-color: #181A1F;
    color: #999999;
    border: none;
    padding: 5px;
    selection-background-color: #24262B;
    text-align: center;
}
#initiatorCombo QAbstractItemView::item {
    height: 25px;
    border: 1px solid #181A1F;
}
#baudCombo QAbstractItemView::item:hover {
    background-color: #181A1F;
}
#initiatorCombo QScrollBar:vertical {
    background: #181A1F;
    width: 0px;
    margin: 0px;
}
#initiatorCombo QScrollBar::handle:vertical {
    background: #999999;
    border-radius: 5px;
    min-height: 20px;
}
#initiatorCombo QScrollBar::add-line:vertical {
    height: 0px;
}
#initiatorCombo QScrollBar::sub-line:vertical {
    height: 0px;
}
#initiatorCombo QScrollBar::add-page:vertical, QScrollBar::sub-page:vertical {
    background: none;
}

/* followerCombo 样式 */
#followerCombo {
    background-color: #181A1F;
    color: #999999;
    border: 1px solid #27292D;
    padding: 5px 20px 5px 20px;
    border-radius: 5px;
    text-align: center;
}
#followerCombo:hover {
    background-color: #24262B;
}
#followerCombo::drop-down {
    border: none;
    width: 20px;
}
#followerCombo QAbstractItemView {
    background-color: #181A1F;
    color: #999999;
    border: none;
    padding: 5px;
    selection-background-color: #24262B;
    text-align: center;
}
#followerCombo QAbstractItemView::item {
    height: 25px;
    border: 1px solid #181A1F;
}
#baudCombo QAbstractItemView::item:hover {
    background-color: #181A1F;
}
#followerCombo QScrollBar:vertical {
    background: #181A1F;
    width: 0px;
    margin: 0px;
}
#followerCombo QScrollBar::handle:vertical {
    background: #999999;
    border-radius: 5px;
    min-height: 20px;
}
#followerCombo QScrollBar::add-line:vertical {
    height: 0px;
}
#followerCombo QScrollBar::sub-line:vertical {
    height: 0px;
}
#followerCombo QScrollBar::add-page:vertical, QScrollBar::sub-page:vertical {
    background: none;
}

/* opcodeCombo 样式 */
#opcodeCombo {
    background-color: #181A1F;
    color: #999999;
    border: 1px solid #27292D;
    padding: 5px 20px 5px 20px;
    border-radius: 5px;
    text-align: center;
}
#opcodeCombo:hover {
    background-color: #24262B;
}
#opcodeCombo::drop-down {
    border: none;
    width: 20px;
}
#opcodeCombo QAbstractItemView {
    background-color: #181A1F;
    color: #999999;
    border: none;
    padding: 5px;
    selection-background-color: #24262B;
    text-align: center;
}
#opcodeCombo QAbstractItemView::item {
    height: 25px;
    border: 1px solid #181A1F;
}
#baudCombo QAbstractItemView::item:hover {
    background-color: #181A1F;
}
#opcodeCombo QScrollBar:vertical {
    background: #181A1F;
    width: 0px;
    margin: 0px;
}
#opcodeCombo QScrollBar::handle:vertical {
    background: #999999;
    border-radius: 5px;
    min-height: 20px;
}
#opcodeCombo QScrollBar::add-line:vertical {
    height: 0px;
}
#opcodeCombo QScrollBar::sub-line:vertical {
    height: 0px;
}
#opcodeCombo QScrollBar::add-page:vertical, QScrollBar::sub-page:vertical {
    background: none;
}

/* CheckBox 样式 */
QCheckBox {
    color: #999999;
    background-color: #181A1F;
    border: 1px solid #27292D;
    border-radius: 5px;
    padding: 5px;
}
QCheckBox::indicator {
    border-radius: 5px;
    background-color: #181A1F;
}
QCheckBox::indicator:checked {
    background-color: green;
}
QCheckBox::indicator:unchecked {
    background-color: #999999;
}

/* Splitter 分隔条样式 */
QSplitter::handle {
    background-color: #27292D;
    height: 1px;
}