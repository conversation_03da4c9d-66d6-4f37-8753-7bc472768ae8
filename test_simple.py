#!/usr/bin/env python3
"""
简单的Slint测试
"""

import sys
import os

def main():
    try:
        # 尝试导入slint
        import slint
        print("✓ Slint imported successfully")
        
        # 加载简单的UI文件
        ui = slint.load_file("simple_test.slint")
        print("✓ Simple UI compiled successfully")
        
        # 创建实例
        window = ui()
        print("✓ Window instance created")
        
        # 显示窗口
        window.show()
        print("✓ Window shown")
        
        # 运行事件循环
        print("Starting event loop...")
        slint.run_event_loop()
        
    except ImportError as e:
        print(f"✗ Import error: {e}")
        print("Please install slint: pip install slint")
        return 1
    except Exception as e:
        print(f"✗ Error: {e}")
        import traceback
        traceback.print_exc()
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
