#!/usr/bin/env python3
"""
安装依赖脚本
"""

import sys
import subprocess
import logging

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger('InstallDeps')

def run_command(command):
    """运行命令并返回结果"""
    try:
        logger.info(f"Running: {command}")
        result = subprocess.run(command, shell=True, capture_output=True, text=True)
        
        if result.returncode == 0:
            logger.info(f"✓ Command succeeded")
            if result.stdout:
                logger.info(f"Output: {result.stdout.strip()}")
            return True
        else:
            logger.error(f"✗ Command failed with return code {result.returncode}")
            if result.stderr:
                logger.error(f"Error: {result.stderr.strip()}")
            return False
    except Exception as e:
        logger.error(f"✗ Exception running command: {e}")
        return False

def check_python_version():
    """检查Python版本"""
    version = sys.version_info
    logger.info(f"Python version: {version.major}.{version.minor}.{version.micro}")
    
    if version.major < 3 or (version.major == 3 and version.minor < 8):
        logger.error("✗ Python 3.8+ is required")
        return False
    
    logger.info("✓ Python version is compatible")
    return True

def install_dependencies():
    """安装依赖"""
    dependencies = [
        "pyserial>=3.5",
        "slint>=1.8.0"
    ]
    
    for dep in dependencies:
        logger.info(f"\n--- Installing {dep} ---")
        if not run_command(f"pip install {dep}"):
            logger.error(f"Failed to install {dep}")
            return False
    
    return True

def verify_installation():
    """验证安装"""
    logger.info("\n--- Verifying installation ---")
    
    # 测试导入
    try:
        import serial
        logger.info("✓ pyserial imported successfully")
    except ImportError as e:
        logger.error(f"✗ Failed to import pyserial: {e}")
        return False
    
    try:
        import slint
        logger.info("✓ slint imported successfully")
        logger.info(f"Slint version: {slint.__version__ if hasattr(slint, '__version__') else 'unknown'}")
    except ImportError as e:
        logger.error(f"✗ Failed to import slint: {e}")
        return False
    
    return True

def main():
    """主函数"""
    logger.info("Starting dependency installation...")
    
    # 检查Python版本
    if not check_python_version():
        return 1
    
    # 升级pip
    logger.info("\n--- Upgrading pip ---")
    if not run_command("python -m pip install --upgrade pip"):
        logger.warning("Failed to upgrade pip, continuing anyway...")
    
    # 安装依赖
    if not install_dependencies():
        logger.error("✗ Failed to install dependencies")
        return 1
    
    # 验证安装
    if not verify_installation():
        logger.error("✗ Installation verification failed")
        return 1
    
    logger.info("\n✓ All dependencies installed successfully!")
    logger.info("You can now run: python main.py")
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
