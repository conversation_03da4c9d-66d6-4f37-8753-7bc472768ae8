from PySide6.QtCore import Qt, QPoint, QSize # type: ignore
from PySide6.QtWidgets import <PERSON><PERSON>ram<PERSON>, QPushButton, QLabel, QHBoxLayout # type: ignore

class TitleBar(QFrame):
    """自定义标题栏类，负责拖动和最大化/还原功能"""

    def __init__(self, parent):
        super().__init__(parent)
        self.parent = parent
        self.setFixedHeight(30)
        self.setObjectName("titleBar")

        self.layout = QHBoxLayout(self)
        self.layout.setContentsMargins(0, 0, 0, 0)
        self.layout.setSpacing(5)

        # 标题图标和文本
        self.title_icon = QLabel()
        self.title_icon.setObjectName("titleIcon")
        if hasattr(parent, 'windowIcon') and not parent.windowIcon().isNull():
            pixmap = parent.windowIcon().pixmap(QSize(30, 30))
            self.title_icon.setPixmap(pixmap)
        self.title_icon.setContentsMargins(5, 0, 0, 0)
        self.layout.addWidget(self.title_icon)

        self.title_label = QLabel(parent.windowTitle())
        self.title_label.setObjectName("titleLabel")
        self.layout.addWidget(self.title_label)
        self.layout.addStretch()

        # 最小化、最大化/还原、关闭按钮
        self.btn_minimize = QPushButton("—")
        self.btn_minimize.setFixedSize(30, 30)
        self.btn_minimize.setObjectName("minimizeButton")
        self.btn_minimize.clicked.connect(parent.showMinimized)

        self.btn_maximize = QPushButton("□")
        self.btn_maximize.setFixedSize(30, 30)
        self.btn_maximize.setObjectName("maximizeButton")
        self.btn_maximize.clicked.connect(self.toggle_maximize)

        self.btn_close = QPushButton("x")
        self.btn_close.setFixedSize(30, 30)
        self.btn_close.setObjectName("closeButton")
        self.btn_close.clicked.connect(parent.close)

        self.layout.addWidget(self.btn_minimize)
        self.layout.addWidget(self.btn_maximize)
        self.layout.addWidget(self.btn_close)

        # 拖动相关变量
        self.dragging = False
        self.drag_position = QPoint()

        # 启用鼠标跟踪
        self.setMouseTracking(True)

    def toggle_maximize(self):
        """切换最大化/还原状态"""
        if self.parent.isMaximized():
            self.parent.showNormal()
            self.btn_maximize.setText("□")
        else:
            self.parent.showMaximized()
            self.btn_maximize.setText("❐")

    def mousePressEvent(self, event):
        """处理鼠标按下事件，用于拖动"""
        if self.parent.isMaximized():
            return
        if event.button() == Qt.MouseButton.LeftButton:
            self.dragging = True
            self.drag_position = event.globalPosition().toPoint() - self.parent.frameGeometry().topLeft()
            event.accept()

    def mouseMoveEvent(self, event):
        """处理鼠标移动事件，用于拖动"""
        if self.parent.isMaximized():
            return
        if self.dragging and event.buttons() & Qt.MouseButton.LeftButton:
            self.parent.move(event.globalPosition().toPoint() - self.drag_position)
            event.accept()

    def mouseReleaseEvent(self, event):
        """处理鼠标释放事件，结束拖动"""
        self.dragging = False
        event.accept()

    def mouseDoubleClickEvent(self, event):
        """双击标题栏切换最大化/还原"""
        self.toggle_maximize()
        event.accept()