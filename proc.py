import logging
from queue import Queue
from PySide6.QtCore import QThread, Signal # type: ignore

logger = logging.getLogger('Processor')

class DataProcessor(QThread):
    data_received = Signal(object)

    def __init__(self, com):
        super().__init__()
        self.com = com
        self.running = False
        self.data_queue = Queue()

    def run(self):
        self.running = True
        logger.info(f"Thread {self.__class__.__name__} started")
        while self.running:
            if self.com.is_port_open():
                result = self.com.receive_data()
                if result:
                    frame, timestamp = result
                    self.data_queue.put((frame, timestamp))
                    # logger.debug(f"Added to queue: {frame.hex()}, queue size: {self.data_queue.qsize()}")
                    processed_data = self.process_queue()
                    if processed_data:
                        self.data_received.emit(processed_data)
                        # logger.debug(f"Emitted processed data: {processed_data}")
            else:
                logger.debug("Serial port not open, waiting...")
                self.msleep(500)  # 串口不可用时稍长等待
            self.msleep(5)
        logger.info(f"Thread {self.__class__.__name__} stopped")

    def stop(self):
        self.running = False
        self.wait(2000)
        if self.isRunning():
            logger.warning(f"Thread {self.__class__.__name__} did not stop in time, terminating")
            self.terminate()
            self.wait(1000)

    def send(self, data):
        raise NotImplementedError("Subclasses must implement send")

    def process_queue(self):
        raise NotImplementedError("Subclasses must implement process_queue")