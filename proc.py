import logging
import threading
import time
from queue import Queue

logger = logging.getLogger('Processor')

class DataProcessor(threading.Thread):
    def __init__(self, com):
        super().__init__()
        self.com = com
        self.running = False
        self.data_queue = Queue()
        self.data_callback = None
        self.daemon = True

    def set_data_callback(self, callback):
        """设置数据回调函数"""
        self.data_callback = callback

    def run(self):
        self.running = True
        logger.info(f"Thread {self.__class__.__name__} started")
        while self.running:
            if self.com.is_port_open():
                result = self.com.receive_data()
                if result:
                    frame, timestamp = result
                    self.data_queue.put((frame, timestamp))
                    processed_data = self.process_queue()
                    if processed_data and self.data_callback:
                        self.data_callback(processed_data)
            else:
                logger.debug("Serial port not open, waiting...")
                time.sleep(0.5)  # 串口不可用时稍长等待
            time.sleep(0.005)  # 5ms
        logger.info(f"Thread {self.__class__.__name__} stopped")

    def stop(self):
        self.running = False

    def wait(self, timeout_ms):
        """等待线程结束"""
        timeout_sec = timeout_ms / 1000.0
        self.join(timeout_sec)
        return not self.is_alive()

    def isRunning(self):
        """检查线程是否在运行"""
        return self.is_alive()

    def send(self, data):
        raise NotImplementedError("Subclasses must implement send")

    def process_queue(self):
        raise NotImplementedError("Subclasses must implement process_queue")