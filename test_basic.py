#!/usr/bin/env python3
"""
基本功能测试脚本（不依赖Slint）
"""

import sys
import logging

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger('BasicTest')

def test_serial_import():
    """测试串口模块导入"""
    try:
        import serial
        logger.info("✓ pyserial imported successfully")
        return True
    except Exception as e:
        logger.error(f"✗ pyserial import failed: {e}")
        return False

def test_com_without_slint():
    """测试串口通信（不依赖Slint）"""
    try:
        # 临时修改com.py以移除Slint依赖进行测试
        import sys
        import glob
        import serial
        import logging
        from datetime import datetime
        import serial.tools.list_ports

        class TestSerialCom:
            def __init__(self):
                self.serial_port = None
                self.buffer = bytearray()
                self.separators = [b'\r\n', b'\n\r', b'\n', b'\r']
                self.is_cec_debug = False
                self.port_reopened_callback = None

            def set_port_reopened_callback(self, callback):
                self.port_reopened_callback = callback

            @staticmethod
            def get_available_ports():
                if sys.platform.startswith('win'):
                    ports = ['COM%s' % (i + 1) for i in range(256)]
                elif sys.platform.startswith('linux') or sys.platform.startswith('cygwin'):
                    ports = glob.glob('/dev/tty[A-Za-z]*')
                elif sys.platform.startswith('darwin'):
                    ports = glob.glob('/dev/tty.*')
                else:
                    raise EnvironmentError('Unsupported platform')
                result = []
                for port in ports:
                    try:
                        s = serial.Serial(port)
                        s.close()
                        result.append(port)
                    except (OSError, serial.SerialException):
                        pass
                return result

            def is_port_open(self):
                return self.serial_port and self.serial_port.is_open

            def open_port(self, port, baudrate):
                try:
                    self.serial_port = serial.Serial(port, baudrate, timeout=0.1)
                    return True
                except Exception:
                    return False

            def close_port(self):
                if self.serial_port and self.serial_port.is_open:
                    self.serial_port.close()

        com = TestSerialCom()
        ports = com.get_available_ports()
        logger.info(f"✓ TestSerialCom created successfully, found ports: {ports}")
        return True
    except Exception as e:
        logger.error(f"✗ TestSerialCom creation failed: {e}")
        return False

def test_threading():
    """测试线程功能"""
    try:
        import threading
        import time
        from queue import Queue

        class TestProcessor(threading.Thread):
            def __init__(self):
                super().__init__()
                self.running = False
                self.data_queue = Queue()
                self.data_callback = None
                self.daemon = True

            def set_data_callback(self, callback):
                self.data_callback = callback

            def run(self):
                self.running = True
                count = 0
                while self.running and count < 3:
                    if self.data_callback:
                        self.data_callback(f"Test data {count}")
                    time.sleep(0.1)
                    count += 1

            def stop(self):
                self.running = False

        results = []
        def test_callback(data):
            results.append(data)

        processor = TestProcessor()
        processor.set_data_callback(test_callback)
        processor.start()
        processor.join(timeout=1.0)
        processor.stop()

        logger.info(f"✓ Threading test passed, received: {results}")
        return True
    except Exception as e:
        logger.error(f"✗ Threading test failed: {e}")
        return False

def main():
    """主测试函数"""
    logger.info("Starting basic functionality tests...")
    
    tests = [
        ("Serial Import Test", test_serial_import),
        ("SerialCom Test", test_com_without_slint),
        ("Threading Test", test_threading),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        logger.info(f"\n--- Running {test_name} ---")
        if test_func():
            passed += 1
        else:
            logger.error(f"{test_name} failed!")
    
    logger.info(f"\n--- Test Results ---")
    logger.info(f"Passed: {passed}/{total}")
    
    if passed == total:
        logger.info("✓ All basic tests passed!")
        return 0
    else:
        logger.error("✗ Some basic tests failed!")
        return 1

if __name__ == "__main__":
    sys.exit(main())
