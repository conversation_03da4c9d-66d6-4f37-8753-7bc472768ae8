import { Button, VerticalBox, HorizontalBox } from "std-widgets.slint";

export component SimpleTest inherits Window {
    title: "Simple Test";
    preferred-width: 400px;
    preferred-height: 300px;
    
    VerticalBox {
        padding: 20px;
        spacing: 10px;
        
        Text {
            text: "Hello Slint!";
            font-size: 18px;
            horizontal-alignment: center;
        }
        
        HorizontalBox {
            spacing: 10px;
            
            Button {
                text: "Button 1";
                clicked => {
                    debug("Button 1 clicked");
                }
            }
            
            Button {
                text: "Button 2";
                clicked => {
                    debug("Button 2 clicked");
                }
            }
        }
    }
}
