#!/usr/bin/env python3
"""
最小化的GUI测试
"""

import sys
import logging
from utils import resource_path

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger('MinimalGUI')

class MinimalSlintGUI:
    def __init__(self):
        try:
            import slint
            logger.info("✓ Slint imported successfully")
            
            # 加载Slint UI文件
            self.ui = slint.load_file(resource_path("serial_gui.slint"))
            logger.info("✓ Slint UI file loaded successfully")
            
            # 创建UI实例
            self.window = self.ui()
            logger.info("✓ UI instance created successfully")
            
            # 设置初始值
            self._setup_initial_values()
            
        except Exception as e:
            logger.error(f"✗ Failed to initialize GUI: {e}")
            raise
    
    def _setup_initial_values(self):
        """设置初始值"""
        try:
            # 设置波特率列表
            baud_rates = ["19200", "38400", "57600", "115200", "128000", "256000", "460800", "921600"]
            self.window.set_baud_rates(baud_rates)
            self.window.set_selected_baud("115200")
            
            # 设置发送结束符列表
            send_ends = ["-", "\\r", "\\n", "\\r\\n", "\\n\\r"]
            self.window.set_send_ends(send_ends)
            self.window.set_selected_send_end("-")
            
            # 设置版本信息
            self.window.set_version("1.0.0")
            self.window.set_copyright("© 2024")
            
            # 设置默认值
            self.window.set_repeat_interval("10")
            self.window.set_timestamp(True)
            
            logger.info("✓ Initial values set successfully")
            
        except Exception as e:
            logger.error(f"✗ Failed to set initial values: {e}")
            # 继续运行，即使设置失败
    
    def show(self):
        """显示窗口"""
        try:
            self.window.show()
            logger.info("✓ Window shown successfully")
        except Exception as e:
            logger.error(f"✗ Failed to show window: {e}")
            raise
    
    def run(self):
        """运行事件循环"""
        try:
            import slint
            logger.info("Starting Slint event loop...")
            slint.run_event_loop()
            logger.info("Event loop finished")
        except Exception as e:
            logger.error(f"✗ Event loop error: {e}")
            raise

def main():
    """主函数"""
    try:
        logger.info("Starting minimal GUI test...")
        
        # 创建GUI
        gui = MinimalSlintGUI()
        
        # 显示窗口
        gui.show()
        
        # 运行事件循环
        gui.run()
        
        logger.info("✓ Application finished successfully")
        return 0
        
    except ImportError as e:
        logger.error(f"✗ Import error: {e}")
        logger.error("Please install slint: pip install slint")
        return 1
    except Exception as e:
        logger.error(f"✗ Application error: {e}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    sys.exit(main())
