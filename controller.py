import logging
from gui import PortRefreshThread
from PySide6.QtCore import Qt, QTimer # type: ignore
from cec_proc import CEC_Processor
from normal_proc import Normal_Processor
from cec_input_validator import CECInputValidator
from cec_definitions import CEC_OPCODES, CEC_LOGIC_ADDRESSES

# 在文件开头定义 logger
logger = logging.getLogger('Controller')

class QTextEditHandler(logging.Handler):
    def __init__(self, gui):
        super().__init__()
        self.gui = gui

    def emit(self, record):
        msg = self.format(record)
        # 根据日志级别设置颜色
        # color_settings = {
        #     logging.DEBUG: {"text": "#A0B8C8", "bg": "transparent"},  # 提亮的浅蓝灰
        #     logging.INFO: {"text": "#A0C090", "bg": "transparent"},  # 提亮的淡绿色
        #     logging.WARNING: {"text": "#E0B580", "bg": "transparent"},  # 提亮的浅橙色
        #     logging.ERROR: {"text": "#E08080", "bg": "#454B57"},  # 提亮的浅红色，稍亮灰背景
        #     logging.CRITICAL: {"text": "#C06060", "bg": "#454B57"}  # 提亮的深红色，稍亮灰背景
        # }
        
        # # 获取当前级别的颜色和背景色
        # settings = color_settings.get(record.levelno, {"text": "#C0C0C0", "bg": "transparent"})  # 默认提亮灰色
        # text_color = settings["text"]
        # bg_color = settings["bg"]
        # # 使用 HTML 格式添加颜色
        # if bg_color == "transparent":
        #     colored_msg = f'<span style="color:{text_color}">{msg}</span>'
        # else:
        #     colored_msg = f'<span style="color:{text_color}; background-color:{bg_color}; padding: 2px;">{msg}</span>'

        # 使用纯色文字，无背景色
        color_settings = {
            logging.DEBUG: "cyan",      # #00FFFF
            logging.INFO: "green",      # #008000
            logging.WARNING: "yellow",  # #FFFF00
            logging.ERROR: "red",       # #FF0000
            logging.CRITICAL: "red"     # #FF0000
        }
        
        # 获取当前级别的颜色
        text_color = color_settings.get(record.levelno, "white")  # 默认白色
        # 使用 HTML 设置纯色文字
        colored_msg = f'<span style="color:{text_color}">{msg}</span>'

        # 使用柔和的纯色文字，与 #282C34 背景搭配
        # color_settings = {
        #     logging.DEBUG: "skyblue",       # #87CEEB
        #     logging.INFO: "green",          # #008000
        #     logging.WARNING: "darkgoldenrod",  # #B8860B
        #     logging.ERROR: "indianred",     # #CD5C5C
        #     logging.CRITICAL: "darkred"     # #8B0000
        # }
        
        # # 获取当前级别的颜色
        # text_color = color_settings.get(record.levelno, "white")  # 默认白色
        
        # # 使用 HTML 设置纯色文字
        # colored_msg = f'<span style="color:{text_color}">{msg}</span>'
        self.gui.append_debug(colored_msg)

class SerialController:
    def __init__(self, serial_com, gui):
        self.com = serial_com
        thread = self.com.thread()
        thread_name = thread.objectName() if thread else "None"
        logger.debug(f"SerialController initialized with SerialCom, com thread: {thread_name}, thread id: {id(thread) if thread else 'None'}")
        self.normal_proc = Normal_Processor(self.com)
        self.cec_proc = CEC_Processor(self.com)
        self.active_proc = self.normal_proc
        self.gui = gui  # 使用传入的 gui 实例
        self.repeat_timer = None  # 用于重复发送的定时器
        self.rx_total = 0  # 接收字符数计数器
        self.tx_total = 0  # 发送字符数计数器

        # 显式连接信号
        self.com.port_reopened.connect(self.handle_port_reopened)
        logger.debug("Connected port_reopened signal to handle_port_reopened")

        # 初始化 input_validator
        self.input_validator = CECInputValidator(self.gui.send_entry, self.gui.opcode_combo)
        self.input_validator.connect()

        self.update_ports()  # 调用 update_ports 初始化端口列表
        self.port_thread = PortRefreshThread(self.com)
        self.port_thread.ports_updated.connect(self.update_ports)
        self.port_thread.start()

        self.gui.connect_btn.clicked.connect(self.toggle_connection)
        self.gui.send_btn.clicked.connect(self.send_data)
        self.gui.clear_btn.clicked.connect(self.clear_text)
        self.gui.save_btn.clicked.connect(self.save_text)
        self.gui.clear_send_btn.clicked.connect(self.clear_send)
        self.gui.display_mode_checkbox.stateChanged.connect(self.toggle_display_mode)
        self.gui.cec_debug_checkbox.stateChanged.connect(self.toggle_cec_debug)
        self.gui.send_hex_checkbox.stateChanged.connect(self.toggle_send_mode)
        self.gui.timestamp_checkbox.stateChanged.connect(self.toggle_timestamp_mode)
        self.gui.repeat_checkbox.stateChanged.connect(self.toggle_repeat)
        self.normal_proc.data_received.connect(self.update_ui)
        self.cec_proc.data_received.connect(self.update_ui)

        # 监听 opcode 变化
        self.gui.initiator_combo.currentTextChanged.connect(self.log_initiator_change)
        self.gui.follower_combo.currentTextChanged.connect(self.log_follower_change)
        self.gui.opcode_combo.currentTextChanged.connect(
            lambda text: (
                self.gui.send_entry.clear(),
                self.log_opcode_change(text),
                self.gui.send_entry.setPlaceholderText(self.input_validator.get_param_hint())
            )
        )

        self.gui.setup_combo_logging(self.gui.port_combo, "Select Port")
        self.gui.setup_combo_logging(self.gui.baud_combo, "Select Baudrate")
        self.gui.setup_combo_logging(self.gui.send_end_combo, "Select End")

        # 初始化时禁用第二排和第五排控件
        self.set_controls_enabled(False)

    def set_controls_enabled(self, enabled):
        """启用或禁用第一, 二排和第五排控件"""
        # 第一排控件
        self.gui.clear_btn.setEnabled(enabled)
        self.gui.save_btn.setEnabled(enabled)

        # 第二排控件
        self.gui.cec_debug_checkbox.setEnabled(enabled)
        self.set_second_row_controls_enabled(enabled)

        # 第三排控件
        self.gui.recv_text.setEnabled(enabled)

        # 第五排控件
        self.gui.initiator_combo.setEnabled(enabled)
        self.gui.follower_combo.setEnabled(enabled)
        self.gui.opcode_combo.setEnabled(enabled)
        self.gui.send_entry.setEnabled(enabled)
        self.gui.send_btn.setEnabled(enabled)
        self.gui.clear_send_btn.setEnabled(enabled)

        logger.debug(f"Controls enabled: {enabled}")

    def set_second_row_controls_enabled(self, enabled):
        """启用或禁用第二排控件"""
        self.gui.display_mode_checkbox.setEnabled(enabled)
        self.gui.send_hex_checkbox.setEnabled(enabled)
        self.gui.timestamp_checkbox.setEnabled(enabled)
        self.gui.repeat_checkbox.setEnabled(enabled)
        self.gui.send_end_combo.setEnabled(enabled)
        self.gui.repeat_entry.setEnabled(enabled)
        self.gui.ms_label.setEnabled(enabled)

    def toggle_connection(self):
        if self.gui.connect_btn.text() == "Connect":
            port = self.gui.port_combo.currentText()
            baudrate = int(self.gui.baud_combo.currentText())
            if self.com.open_port(port, baudrate):
                self.gui.connect_btn.setText("Disconnect")
                self.gui.port_combo.setEnabled(False)
                self.gui.baud_combo.setEnabled(False)
                self.restart_processor()
                self.active_proc.start()
                self.gui.update_connect_status(True)  # 更新连接状态
                self.set_controls_enabled(True)  # 连接成功后启用控件
                is_cec_on = self.gui.cec_debug_checkbox.isChecked()
                if is_cec_on:
                    self.set_second_row_controls_enabled(False)
                logger.info(f"Connected to {port}, processor started: {type(self.active_proc).__name__}")
        else:
            # 先停止线程，再关闭串口
            self.active_proc.stop()
            self.com.close_port()
            self.gui.connect_btn.setText("Connect")
            self.gui.port_combo.setEnabled(True)
            self.gui.baud_combo.setEnabled(True)
            self.gui.update_connect_status(False)  # 更新连接状态
            self.set_controls_enabled(False)  # 断开连接后禁用控件
            if self.repeat_timer:
                self.repeat_timer.stop()
                self.repeat_timer = None
                logger.info("Repeat timer stopped")
            logger.info("Disconnected and processor stopped")

    def send_data(self):
        data = self.gui.send_entry.text().strip()  # 去除首尾空格
        terminator = self.gui.get_selected_terminator()

        # CEC debug 模式允许空输入，非 CEC 模式要求非空
        if not self.gui.cec_debug_checkbox.isChecked() and not data:
            logger.warning("Attempted to send empty content")
            return

        try:
            if self.gui.cec_debug_checkbox.isChecked():
                # CEC 模式发送
                initiator = next(k for k, v in CEC_LOGIC_ADDRESSES.items() 
                               if v == self.gui.initiator_combo.currentText())
                follower = next(k for k, v in CEC_LOGIC_ADDRESSES.items() 
                              if v == self.gui.follower_combo.currentText())
                opcode = next(k for k, v in CEC_OPCODES.items() 
                            if v == self.gui.opcode_combo.currentText())
                
                header_byte = (initiator << 4) | follower
                
                # 检查参数长度
                is_valid, params = self.input_validator.check_params_length(data, opcode)
                if not is_valid:
                    return  # 长度不符合要求，不发送
                
                # 构造并发送数据
                cec_data = bytes([header_byte, opcode]) + params
                
                self.active_proc.send(cec_data.hex())
                self.tx_total += len(cec_data)
            else:
                # 非 CEC 模式发送
                if terminator:
                    if self.active_proc.send_hex:
                        data += ' ' + ' '.join(f'{b:02X}' for b in terminator)
                    else:
                        data += terminator.decode('ascii')
                
                self.active_proc.send(data)
                self.tx_total += len(data)

            self.gui.update_tx_total(self.tx_total)
            if self.gui.repeat_checkbox.isChecked():
                interval = max(10, int(self.gui.repeat_entry.text() or 10))
                if not self.repeat_timer:
                    self.repeat_timer = QTimer()
                    self.repeat_timer.timeout.connect(lambda: self.send_data())  # 修改为直接调用 send_data
                    self.repeat_timer.start(interval)
                    logger.info(f"Started repeating data every {interval}ms")
        except ValueError as e:
            logger.error(f"Send error: {str(e)}")

    def save_text(self):
        self.gui.save_text()

    def clear_send(self):
        self.gui.send_entry.clear()
        if self.repeat_timer:
            self.repeat_timer.stop()
            self.repeat_timer = None
        self.tx_total = 0  # 添加Tx计数清零
        self.gui.update_tx_total(self.tx_total)  # 更新UI显示
        logger.debug("Send entry and Tx counter cleared")

    def toggle_display_mode(self, state):
        if hasattr(self.active_proc, 'set_display_mode'):
            self.active_proc.set_display_mode(state == Qt.CheckState.Checked.value)
            logger.debug(f"Display mode set to: {state == Qt.CheckState.Checked.value}")

    def toggle_cec_debug(self, state):
        is_cec_on = (state == Qt.CheckState.Checked.value)
        self.input_validator.set_cec_debug(is_cec_on)
        self.com.is_cec_debug = is_cec_on
        logger.info(f"CEC debug mode: {is_cec_on}")
        if self.active_proc.isRunning():
            self.active_proc.stop()
            self.active_proc = self.cec_proc if is_cec_on else self.normal_proc
            self.restart_processor()
            self.active_proc.start()
            logger.info(f"Switched to processor: {type(self.active_proc).__name__}")
        else:
            self.active_proc = self.cec_proc if is_cec_on else self.normal_proc
            logger.debug(f"Processor set to: {type(self.active_proc).__name__}, not started yet")

        # 控制第二排控件可用性
        self.set_second_row_controls_enabled(not is_cec_on)

        # 显示/隐藏 CEC 控件
        self.gui.initiator_combo.setVisible(is_cec_on)
        self.gui.follower_combo.setVisible(is_cec_on)
        self.gui.opcode_combo.setVisible(is_cec_on)
        
        # CEC debug 打开时强制 HEX 模式，但不改变 UI
        if is_cec_on:
            self.input_validator.set_hex_mode(True)  # 强制 HEX，但不影响 send_hex_checkbox
            self.gui.send_entry.setPlaceholderText(self.input_validator.get_param_hint())
        else:
            if self.gui.send_hex_checkbox.isChecked():
                self.gui.send_entry.setPlaceholderText("Enter data in HEX")
            else:
                self.gui.send_entry.setPlaceholderText("Enter data to send")
            # CEC debug 关闭时，恢复 send_hex_checkbox 的状态
            self.input_validator.set_hex_mode(self.gui.send_hex_checkbox.isChecked())

        self.clear_text()  # 清空接收文本
        self.clear_send()  # 清空发送文本

    @staticmethod
    def log_initiator_change(text):
        logger.info(f"Initiator changed to: {text}")

    @staticmethod
    def log_follower_change(text):
        logger.info(f"Follower changed to: {text}")

    @staticmethod
    def log_opcode_change(text):
        logger.info(f"Opcode changed to: {text}")

    def toggle_send_mode(self, state):
        if hasattr(self.active_proc, 'set_send_mode'):
            self.active_proc.set_send_mode(state == Qt.CheckState.Checked.value)
            logger.debug(f"Send mode set to: {state == Qt.CheckState.Checked.value}")
            self.input_validator.set_hex_mode(state == Qt.CheckState.Checked.value)
            # 更新提示文本
            if state == Qt.CheckState.Checked.value:
                self.gui.send_entry.setPlaceholderText("Enter data in HEX")
            else:
                self.gui.send_entry.setPlaceholderText("Enter data to send")

    def toggle_timestamp_mode(self, state):
        if hasattr(self.active_proc, 'set_timestamp_mode'):
            self.active_proc.set_timestamp_mode(state == Qt.CheckState.Checked.value)
            logger.debug(f"Timestamp mode set to: {state == Qt.CheckState.Checked.value}")

    def toggle_repeat(self, state):
        if not state == Qt.CheckState.Checked.value and self.repeat_timer:
            self.repeat_timer.stop()
            self.repeat_timer = None
            logger.info("Repeat mode disabled")
        logger.debug(f"Repeat mode set to: {state == Qt.CheckState.Checked.value}")

    def clear_text(self):
        self.gui.recv_text.clear()
        self.rx_total = 0  # 重置接收字符数
        self.gui.update_rx_total(self.rx_total)  # 更新接收字符数显示
        logger.debug("Receive text and Rx counter cleared")

    def update_ports(self, ports=None):
        """更新端口列表（增量模式）"""
        # 获取当前显示的端口列表
        current_ports = [self.gui.port_combo.itemText(i) for i in range(self.gui.port_combo.count())]
        
        # 获取最新可用端口列表
        new_ports = ports if ports is not None else self.com.get_available_ports()
        
        # 添加新增端口
        for port in new_ports:
            if port not in current_ports:
                self.gui.port_combo.addItem(port)
        
        # 移除已消失端口
        for i in reversed(range(self.gui.port_combo.count())):
            current_port = self.gui.port_combo.itemText(i)
            if current_port not in new_ports:
                self.gui.port_combo.removeItem(i)
        
        # 保持当前选中项（如果仍然存在）
        current_port = self.gui.port_combo.currentText()
        if current_port not in new_ports and new_ports:
            self.gui.port_combo.setCurrentIndex(0)

    def update_ui(self, result):
        try:
            if result and isinstance(result, dict):
                # 显示原始数据
                # if 'raw_data' in result and result['raw_data']:
                #     logger.debug(f"Raw CEC data: {result['raw_data']}")
                
                # 显示解析后的数据
                if 'parsed_data' in result and result['parsed_data']:
                    self.gui.recv_text.append(result['parsed_data'])
                    self.gui.recv_text.ensureCursorVisible()
                
                # 更新字节计数
                if 'bytes' in result:
                    self.rx_total += result['bytes']
                    self.gui.update_rx_total(self.rx_total)
            else:
                logger.debug("Received invalid or empty result from processor")
        except Exception as e:
            logger.error(f"Error in update_ui: {e}", exc_info=True)

    def show(self):
        self.gui.show()

    def close(self):
        # 停止端口刷新线程
        if self.port_thread.isRunning():
            self.port_thread.stop()
            self.port_thread.wait(2000)  # 等待最多1秒
            if self.port_thread.isRunning():
                logger.warning("Port thread did not stop in time, terminating")
                self.port_thread.terminate()
                self.port_thread.wait(1000)
        
        # 停止数据处理线程
        processors = [self.normal_proc, self.cec_proc]
        for proc in processors:
            if proc.isRunning():
                proc.stop()
                if not proc.wait(2000):  # 等待最多1秒
                    logger.warning(f"Processor {type(proc).__name__} did not stop in time, terminating")
                    proc.terminate()
                    self.port_thread.wait(1000)
        
        # 关闭串口
        self.com.close_port()
        
        # 停止重复定时器
        if self.repeat_timer and self.repeat_timer.isActive():
            self.repeat_timer.stop()
            self.repeat_timer = None
        
        self.gui.close()
        logger.info("Application closed cleanly")

    def handle_port_reopened(self):
        logger.info("Received port reopened signal, restarting processor")
        self.restart_processor()
    
    def restart_processor(self):
        if self.active_proc.isRunning():
            self.active_proc.stop()
            self.active_proc.wait(2000)
            if self.active_proc.isRunning():
                logger.warning(f"Processor {type(self.active_proc).__name__} did not stop in time")
        self.active_proc.start()
        logger.info(f"Processor restarted: {type(self.active_proc).__name__}")