import logging
import threading
from gui import PortRefreshThread
from cec_proc import CEC_Processor
from normal_proc import Normal_Processor
from cec_input_validator import CECInputValidator
from cec_definitions import CEC_OPCODES, CEC_LOGIC_ADDRESSES

# 在文件开头定义 logger
logger = logging.getLogger('Controller')

class QTextEditHandler(logging.Handler):
    def __init__(self, gui):
        super().__init__()
        self.gui = gui

    def emit(self, record):
        msg = self.format(record)
        # 根据日志级别设置颜色
        # color_settings = {
        #     logging.DEBUG: {"text": "#A0B8C8", "bg": "transparent"},  # 提亮的浅蓝灰
        #     logging.INFO: {"text": "#A0C090", "bg": "transparent"},  # 提亮的淡绿色
        #     logging.WARNING: {"text": "#E0B580", "bg": "transparent"},  # 提亮的浅橙色
        #     logging.ERROR: {"text": "#E08080", "bg": "#454B57"},  # 提亮的浅红色，稍亮灰背景
        #     logging.CRITICAL: {"text": "#C06060", "bg": "#454B57"}  # 提亮的深红色，稍亮灰背景
        # }
        
        # # 获取当前级别的颜色和背景色
        # settings = color_settings.get(record.levelno, {"text": "#C0C0C0", "bg": "transparent"})  # 默认提亮灰色
        # text_color = settings["text"]
        # bg_color = settings["bg"]
        # # 使用 HTML 格式添加颜色
        # if bg_color == "transparent":
        #     colored_msg = f'<span style="color:{text_color}">{msg}</span>'
        # else:
        #     colored_msg = f'<span style="color:{text_color}; background-color:{bg_color}; padding: 2px;">{msg}</span>'

        # 使用纯色文字，无背景色
        color_settings = {
            logging.DEBUG: "cyan",      # #00FFFF
            logging.INFO: "green",      # #008000
            logging.WARNING: "yellow",  # #FFFF00
            logging.ERROR: "red",       # #FF0000
            logging.CRITICAL: "red"     # #FF0000
        }
        
        # 获取当前级别的颜色
        text_color = color_settings.get(record.levelno, "white")  # 默认白色
        # 使用 HTML 设置纯色文字
        colored_msg = f'<span style="color:{text_color}">{msg}</span>'

        # 使用柔和的纯色文字，与 #282C34 背景搭配
        # color_settings = {
        #     logging.DEBUG: "skyblue",       # #87CEEB
        #     logging.INFO: "green",          # #008000
        #     logging.WARNING: "darkgoldenrod",  # #B8860B
        #     logging.ERROR: "indianred",     # #CD5C5C
        #     logging.CRITICAL: "darkred"     # #8B0000
        # }
        
        # # 获取当前级别的颜色
        # text_color = color_settings.get(record.levelno, "white")  # 默认白色
        
        # # 使用 HTML 设置纯色文字
        # colored_msg = f'<span style="color:{text_color}">{msg}</span>'
        self.gui.append_debug(colored_msg)

class RepeatTimer:
    """重复定时器类，替代QTimer"""
    def __init__(self, interval, callback):
        self.interval = interval / 1000.0  # 转换为秒
        self.callback = callback
        self.timer = None
        self.is_running = False

    def start(self):
        if not self.is_running:
            self.is_running = True
            self._run()

    def stop(self):
        self.is_running = False
        if self.timer:
            self.timer.cancel()

    def _run(self):
        if self.is_running:
            self.callback()
            self.timer = threading.Timer(self.interval, self._run)
            self.timer.start()

class SerialController:
    def __init__(self, serial_com, gui):
        self.com = serial_com
        logger.debug(f"SerialController initialized with SerialCom")
        self.normal_proc = Normal_Processor(self.com)
        self.cec_proc = CEC_Processor(self.com)
        self.active_proc = self.normal_proc
        self.gui = gui  # 使用传入的 gui 实例
        self.repeat_timer = None  # 用于重复发送的定时器
        self.rx_total = 0  # 接收字符数计数器
        self.tx_total = 0  # 发送字符数计数器

        # 设置GUI回调函数
        self._setup_gui_callbacks()

        # 初始化端口列表
        self.update_ports()
        self.gui.start_port_refresh(self.com)

    def _setup_gui_callbacks(self):
        """设置GUI回调函数"""
        self.gui.set_callback('connect_clicked', self.toggle_connection)
        self.gui.set_callback('send_clicked', self.send_data)
        self.gui.set_callback('clear_clicked', self.clear_text)
        self.gui.set_callback('save_clicked', self.save_text)
        self.gui.set_callback('clear_send_clicked', self.clear_send)

        self.gui.set_callback('cec_debug_changed', self.toggle_cec_debug)
        self.gui.set_callback('rx_hex_changed', self.toggle_display_mode)
        self.gui.set_callback('timestamp_changed', self.toggle_timestamp_mode)
        self.gui.set_callback('tx_hex_changed', self.toggle_send_mode)
        self.gui.set_callback('repeat_changed', self.toggle_repeat)

        self.gui.set_callback('port_changed', self.on_port_changed)
        self.gui.set_callback('baud_changed', self.on_baud_changed)
        self.gui.set_callback('send_end_changed', self.on_send_end_changed)
        self.gui.set_callback('initiator_changed', self.on_initiator_changed)
        self.gui.set_callback('follower_changed', self.on_follower_changed)
        self.gui.set_callback('opcode_changed', self.on_opcode_changed)

        self.gui.set_callback('send_text_changed', self.on_send_text_changed)
        self.gui.set_callback('repeat_interval_changed', self.on_repeat_interval_changed)

        # 连接数据处理器信号 - 需要适配
        self.normal_proc.data_received.connect(self.update_ui)
        self.cec_proc.data_received.connect(self.update_ui)

        # 设置日志处理器
        log_handler = QTextEditHandler(self.gui)
        log_handler.setLevel(logging.DEBUG)
        formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
        log_handler.setFormatter(formatter)

        # 获取根日志记录器并添加处理器
        root_logger = logging.getLogger()
        root_logger.addHandler(log_handler)
        root_logger.setLevel(logging.DEBUG)

        # 初始化时禁用控件
        self.set_controls_enabled(False)

        logger.info("SerialController initialized successfully")

    # 新增的回调函数
    def on_port_changed(self, port):
        logger.info(f"[Port] {port}")

    def on_baud_changed(self, baud):
        logger.info(f"[Baud Rate] {baud}")

    def on_send_end_changed(self, send_end):
        logger.info(f"[Send End] {send_end}")

    def on_initiator_changed(self, initiator):
        logger.info(f"[CEC Initiator] {initiator}")
        # 清空发送文本框
        self.gui.ui.set_send_text("")

    def on_follower_changed(self, follower):
        logger.info(f"[CEC Follower] {follower}")

    def on_opcode_changed(self, opcode):
        logger.info(f"[CEC Opcode] {opcode}")
        # 清空发送文本框并更新提示
        self.gui.ui.set_send_text("")

    def on_send_text_changed(self, text):
        # 可以在这里添加文本变化的处理逻辑
        pass

    def on_repeat_interval_changed(self, interval):
        logger.info(f"Repeat interval changed to: {interval}ms")

    def set_controls_enabled(self, enabled):
        """启用或禁用控件"""
        # 在Slint中，控件的启用状态通过属性控制
        # 这里可以根据需要实现具体的控制逻辑
        self.gui.set_controls_enabled(enabled)
        self.set_second_row_controls_enabled(enabled)
        logger.debug(f"Controls enabled: {enabled}")

    def set_second_row_controls_enabled(self, enabled):
        """启用或禁用第二排控件"""
        # 在Slint中通过属性控制
        self.gui.set_second_row_controls_enabled(enabled)
        logger.debug(f"Second row controls enabled: {enabled}")

    def toggle_connection(self):
        if not self.gui.ui.get_connected():
            port = self.gui.get_selected_port()
            baudrate = int(self.gui.get_selected_baud())
            if self.com.open_port(port, baudrate):
                self.restart_processor()
                self.active_proc.start()
                self.gui.update_connect_status(True)  # 更新连接状态
                self.set_controls_enabled(True)  # 连接成功后启用控件
                is_cec_on = self.gui.is_cec_debug_checked()
                if is_cec_on:
                    self.set_second_row_controls_enabled(False)
                logger.info(f"Connected to {port}, processor started: {type(self.active_proc).__name__}")
        else:
            # 先停止线程，再关闭串口
            self.active_proc.stop()
            self.com.close_port()
            self.gui.update_connect_status(False)  # 更新连接状态
            self.set_controls_enabled(False)  # 断开连接后禁用控件
            if self.repeat_timer:
                self.repeat_timer.stop()
                self.repeat_timer = None
                logger.info("Repeat timer stopped")
            logger.info("Disconnected and processor stopped")

    def send_data(self):
        data = self.gui.get_send_text().strip()  # 去除首尾空格
        terminator = self.gui.get_selected_terminator()

        # CEC debug 模式允许空输入，非 CEC 模式要求非空
        if not self.gui.is_cec_debug_checked() and not data:
            logger.warning("Attempted to send empty content")
            return

        try:
            if self.gui.is_cec_debug_checked():
                # CEC 模式发送
                initiator = next(k for k, v in CEC_LOGIC_ADDRESSES.items()
                               if v == self.gui.get_selected_initiator())
                follower = next(k for k, v in CEC_LOGIC_ADDRESSES.items()
                              if v == self.gui.get_selected_follower())
                opcode = next(k for k, v in CEC_OPCODES.items()
                            if v == self.gui.get_selected_opcode())

                header_byte = (initiator << 4) | follower

                # 检查参数长度 - 需要适配input_validator
                # is_valid, params = self.input_validator.check_params_length(data, opcode)
                # if not is_valid:
                #     return  # 长度不符合要求，不发送

                # 构造并发送数据
                try:
                    params = bytes.fromhex(data) if data else b''
                except ValueError:
                    logger.error(f"Invalid hex data: {data}")
                    return

                cec_data = bytes([header_byte, opcode]) + params

                self.active_proc.send(cec_data.hex())
                self.tx_total += len(cec_data)
            else:
                # 非 CEC 模式发送
                if terminator:
                    if self.active_proc.send_hex:
                        data += ' ' + ' '.join(f'{b:02X}' for b in terminator)
                    else:
                        data += terminator.decode('ascii')

                self.active_proc.send(data)
                self.tx_total += len(data)

            self.gui.update_tx_total(self.tx_total)
            if self.gui.is_repeat_checked():
                interval = max(10, int(self.gui.get_repeat_interval() or 10))
                if not self.repeat_timer:
                    self.repeat_timer = RepeatTimer(interval, self.send_data)
                    self.repeat_timer.start()
                    logger.info(f"Started repeating data every {interval}ms")
        except ValueError as e:
            logger.error(f"Send error: {str(e)}")

    def save_text(self):
        self.gui.save_text()

    def clear_send(self):
        self.gui.clear_send_text()
        if self.repeat_timer:
            self.repeat_timer.stop()
            self.repeat_timer = None
        self.tx_total = 0  # 添加Tx计数清零
        self.gui.update_tx_total(self.tx_total)  # 更新UI显示
        logger.debug("Send entry and Tx counter cleared")

    def toggle_display_mode(self, checked):
        if hasattr(self.active_proc, 'set_display_mode'):
            self.active_proc.set_display_mode(checked)
            logger.debug(f"Display mode set to: {checked}")

    def toggle_cec_debug(self, checked):
        is_cec_on = checked
        # self.input_validator.set_cec_debug(is_cec_on)  # 需要适配
        self.com.is_cec_debug = is_cec_on
        logger.info(f"CEC debug mode: {is_cec_on}")
        if self.active_proc.isRunning():
            self.active_proc.stop()
            self.active_proc = self.cec_proc if is_cec_on else self.normal_proc
            self.restart_processor()
            self.active_proc.start()
            logger.info(f"Switched to processor: {type(self.active_proc).__name__}")
        else:
            self.active_proc = self.cec_proc if is_cec_on else self.normal_proc
            logger.debug(f"Processor set to: {type(self.active_proc).__name__}, not started yet")

        # 控制第二排控件可用性
        self.set_second_row_controls_enabled(not is_cec_on)

        # CEC控件的显示/隐藏在Slint UI中通过条件渲染处理

        self.clear_text()  # 清空接收文本
        self.clear_send()  # 清空发送文本

    def toggle_send_mode(self, checked):
            self.active_proc.set_send_mode(checked)
            logger.debug(f"Send mode set to: {checked}")
            # self.input_validator.set_hex_mode(checked)  # 需要适配

    def toggle_timestamp_mode(self, checked):
        if hasattr(self.active_proc, 'set_timestamp_mode'):
            self.active_proc.set_timestamp_mode(checked)
            logger.debug(f"Timestamp mode set to: {checked}")

    def toggle_repeat(self, checked):
        if not checked and self.repeat_timer:
            self.repeat_timer.stop()
            self.repeat_timer = None
            logger.info("Repeat mode disabled")
        logger.debug(f"Repeat mode set to: {checked}")

    def clear_text(self):
        self.gui.clear_recv_text()
        self.rx_total = 0  # 重置接收字符数
        self.gui.update_rx_total(self.rx_total)  # 更新接收字符数显示
        logger.debug("Receive text and Rx counter cleared")

    def update_ports(self, ports=None):
        """更新端口列表"""
        new_ports = ports if ports is not None else self.com.get_available_ports()
        self.gui.update_port_list(new_ports)

    def update_ui(self, result):
        try:
            if result and isinstance(result, dict):
                # 显示原始数据
                # if 'raw_data' in result and result['raw_data']:
                #     logger.debug(f"Raw CEC data: {result['raw_data']}")
                
                # 显示解析后的数据
                if 'parsed_data' in result and result['parsed_data']:
                    self.gui.recv_text.append(result['parsed_data'])
                    self.gui.recv_text.ensureCursorVisible()
                
                # 更新字节计数
                if 'bytes' in result:
                    self.rx_total += result['bytes']
                    self.gui.update_rx_total(self.rx_total)
            else:
                logger.debug("Received invalid or empty result from processor")
        except Exception as e:
            logger.error(f"Error in update_ui: {e}", exc_info=True)

    def show(self):
        self.gui.show()

    def close(self):
        # 停止端口刷新线程
        if self.port_thread.isRunning():
            self.port_thread.stop()
            self.port_thread.wait(2000)  # 等待最多1秒
            if self.port_thread.isRunning():
                logger.warning("Port thread did not stop in time, terminating")
                self.port_thread.terminate()
                self.port_thread.wait(1000)
        
        # 停止数据处理线程
        processors = [self.normal_proc, self.cec_proc]
        for proc in processors:
            if proc.isRunning():
                proc.stop()
                if not proc.wait(2000):  # 等待最多1秒
                    logger.warning(f"Processor {type(proc).__name__} did not stop in time, terminating")
                    proc.terminate()
                    self.port_thread.wait(1000)
        
        # 关闭串口
        self.com.close_port()
        
        # 停止重复定时器
        if self.repeat_timer and self.repeat_timer.isActive():
            self.repeat_timer.stop()
            self.repeat_timer = None
        
        self.gui.close()
        logger.info("Application closed cleanly")

    def handle_port_reopened(self):
        logger.info("Received port reopened signal, restarting processor")
        self.restart_processor()
    
    def restart_processor(self):
        if self.active_proc.isRunning():
            self.active_proc.stop()
            self.active_proc.wait(2000)
            if self.active_proc.isRunning():
                logger.warning(f"Processor {type(self.active_proc).__name__} did not stop in time")
        self.active_proc.start()
        logger.info(f"Processor restarted: {type(self.active_proc).__name__}")