#!/usr/bin/env python3
"""
测试Slint文件编译
"""

import sys
import logging
from utils import resource_path

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger('SlintCompileTest')

def test_slint_compile():
    """测试Slint文件编译"""
    try:
        import slint
        logger.info("✓ Slint module imported successfully")
        
        # 尝试编译Slint文件
        slint_file = resource_path("serial_gui.slint")
        logger.info(f"Loading Slint file: {slint_file}")
        
        ui = slint.load_file(slint_file)
        logger.info("✓ Slint file compiled successfully")
        
        # 测试创建实例
        instance = ui()
        logger.info("✓ Slint UI instance created successfully")
        
        return True
    except Exception as e:
        logger.error(f"✗ Slint compile test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    logger.info("Testing Slint file compilation...")
    
    if test_slint_compile():
        logger.info("✓ Slint compilation test passed!")
        return 0
    else:
        logger.error("✗ Slint compilation test failed!")
        return 1

if __name__ == "__main__":
    sys.exit(main())
