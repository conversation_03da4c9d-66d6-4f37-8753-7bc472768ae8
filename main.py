import sys
import logging
from com import SerialCom
from gui import SerialGUI
from utils import resource_path
from file_loader import <PERSON><PERSON>oader
from controller import SerialController
from PySide6.QtWidgets import QApplication # type: ignore

logger = logging.getLogger('Main')

def main():
    # 初始化 QApplication
    app = QApplication(sys.argv)

    # 创建 StyleLoader 实例并应用样式
    style_loader = StyleLoader(resource_path_func=resource_path, qss_dir=".", font_dir=".")
    style_loader.apply_styles(app)

    # 创建 SerialGUI 实例
    gui = SerialGUI()

    # 转移日志到 GUI
    style_loader.transfer_logs(gui)

    com = SerialCom()
    main_thread = app.thread()
    main_thread.setObjectName("MainThread")
    com.moveToThread(main_thread)  # 强制绑定到主线程
    thread = com.thread()
    thread_name = thread.objectName() if thread else "None"
    logger.debug(f"SerialCom created in main, thread: {thread_name}, thread id: {id(thread) if thread else 'None'}")

    # 创建 SerialController 实例
    controller = SerialController(com, gui)
    controller.show()

    # 确保关闭时释放资源
    app.aboutToQuit.connect(controller.close)

    logger.info("Starting application event loop")
    exit_code = app.exec()
    logger.info(f"Application exited with code {exit_code}")
    sys.exit(exit_code)

if __name__ == "__main__":
    main()