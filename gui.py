import os
import logging
import datetime
from titlebar import TitleBar
from utils import resource_path
from config import APP_NAME, VERSION, COPYRIGHT
from cec_definitions import CEC_LOGIC_ADDRESSES, CEC_OPCODES
from PySide6.QtGui import QRegularExpressionValidator, QIcon # type: ignore
from PySide6.QtCore import QThread, Signal, Qt, QRegularExpression, QSize # type: ignore
from PySide6.QtWidgets import ( # type: ignore
    QMainWindow, QComboBox, QPushButton, QLineEdit, QTextEdit, QVBoxLayout, 
    QHBoxLayout, QWidget, QCheckBox, QSplitter, QSizePolicy, QLabel
)

baud_rates = ['19200', '38400', '57600', '115200', '128000', '256000', '460800', '921600']
send_ends = ['-', '\\r', '\\n', '\\r\\n', '\\n\\r']

# 定义 logger
logger = logging.getLogger('GUI')

class PortRefreshThread(QThread):
    ports_updated = Signal(list)

    def __init__(self, com):
        super().__init__()
        self.com = com
        self.running = True

    def run(self):
        while self.running:
            if not self.com.is_port_open():
                ports = self.com.get_available_ports()
                self.ports_updated.emit(ports)
            self.msleep(2000)

    def stop(self):
        self.running = False
        self.wait()

class CustomLineEdit(QLineEdit):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setText("10")
        self._validating = False
        self._last_validated_text = "10"

        validator = QRegularExpressionValidator(QRegularExpression(r"\d+"), self)
        self.setValidator(validator)
        self.setContextMenuPolicy(Qt.NoContextMenu)

    def focusOutEvent(self, event):
        """处理输入框失去焦点时的验证"""
        if not self._validating and self.text() != self._last_validated_text:
            self.validate()
        super().focusOutEvent(event)

    def keyPressEvent(self, event):
        """处理回车键时的验证"""
        if event.key() in (Qt.Key_Return, Qt.Key_Enter):
            self.validate()
        super().keyPressEvent(event)

    def validate(self):
        """验证输入值，确保最小为 10ms"""
        if self._validating:
            return  # 如果正在验证，直接返回

        self._validating = True
        try:
            text = self.text().strip()
            
            if not text:
                self.setText("10")
                self._last_validated_text = "10"
                logger.error("Repeat interval was empty, reset to minimum value: 10ms")
                return

            value = int(text)
            if value < 10:
                self.setText("10")
                self._last_validated_text = "10"
                logger.warning(f"Repeat interval {value}ms is less than 10ms, reset to 10ms")
            else:
                self._last_validated_text = text  # 更新最后验证的文本
                logger.info(f"Repeat interval validated successfully: {value}ms")
        finally:
            self._validating = False

class SerialGUI(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle(APP_NAME)
        self.setMinimumSize(1200, 800)  # 最小大小 1200x800
        self.resize(1200, 800)  # 默认大小 1200x800

        # 设置无边框窗口
        self.setWindowFlags(Qt.WindowType.FramelessWindowHint | Qt.WindowType.WindowMinimizeButtonHint)
        self.setAttribute(Qt.WidgetAttribute.WA_TranslucentBackground)

        # 中心部件和外层布局
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        main_layout = QVBoxLayout(central_widget)

        # 添加自定义标题栏
        self.title_bar = TitleBar(self)
        main_layout.addWidget(self.title_bar)
        main_layout.setContentsMargins(5, 5, 5, 5)

        # 加载外部样式表，指定 UTF-8 编码
        # try:
        #     with open(resource_path("style.qss"), "r", encoding="utf-8") as f:
        #         self.setStyleSheet(f.read())
        # except FileNotFoundError:
        #     print("Error: style.qss file not found.")
        # except UnicodeDecodeError as e:
        #     print(f"Error decoding style.qss: {e}")

        # 第一排：顶部控件，均匀排列，跟随缩放
        top_widget = QWidget()
        top_widget.setObjectName("panel")
        self.top_layout = QHBoxLayout(top_widget)
        self.top_layout.setContentsMargins(5, 5, 5, 5)
        self.top_layout.setSpacing(10)

        self.port_combo = QComboBox()
        self.port_combo.setObjectName("portCombo")
        self.port_combo.setFixedWidth(150)
        self.top_layout.addWidget(self.port_combo, stretch=1)

        self.baud_combo = QComboBox()
        self.baud_combo.setObjectName("baudCombo")
        self.baud_combo.addItems(baud_rates)
        self.baud_combo.setFixedWidth(150)
        self.baud_combo.setCurrentText(baud_rates[3])  # 默认波特率 115200
        self.top_layout.addWidget(self.baud_combo, stretch=1)

        self.connect_btn = QPushButton("Connect")
        self.connect_btn.setObjectName("conButton")
        self.top_layout.addWidget(self.connect_btn, stretch=1)

        self.clear_btn = QPushButton("Clear")
        self.clear_btn.setObjectName("clrRxButton")
        self.top_layout.addWidget(self.clear_btn, stretch=1)

        self.save_btn = QPushButton("Save")
        self.save_btn.setObjectName("savButton")
        self.top_layout.addWidget(self.save_btn, stretch=1)

        main_layout.addWidget(top_widget)

        # 第二排：开关控制，横向均匀排列
        switches_widget = QWidget()
        switches_widget.setObjectName("panel")
        self.switches_layout = QHBoxLayout(switches_widget)
        self.switches_layout.setContentsMargins(5, 5, 5, 5)
        self.switches_layout.setSpacing(10)

        self.cec_debug_checkbox = QCheckBox("CEC Debug")
        self.switches_layout.addWidget(self.cec_debug_checkbox, stretch=1)

        self.display_mode_checkbox = QCheckBox("Rx Hex")
        self.switches_layout.addWidget(self.display_mode_checkbox, stretch=1)

        self.timestamp_checkbox = QCheckBox("Timestamp")
        self.timestamp_checkbox.setChecked(True)
        self.switches_layout.addWidget(self.timestamp_checkbox, stretch=1)

        self.send_hex_checkbox = QCheckBox("Tx Hex")
        self.switches_layout.addWidget(self.send_hex_checkbox, stretch=1)

        self.send_end_combo = QComboBox()
        self.send_end_combo.setObjectName("sendCombo")
        self.send_end_combo.addItems(send_ends)
        self.send_end_combo.setFixedWidth(100)
        self.send_end_combo.setCurrentText(send_ends[0])  # 默认none
        self.switches_layout.addWidget(self.send_end_combo, stretch=1)

        self.repeat_checkbox = QCheckBox("Repeat")
        self.switches_layout.addWidget(self.repeat_checkbox, stretch=1)

        self.repeat_entry = CustomLineEdit()
        self.repeat_entry.setMaximumWidth(60)
        # self.repeat_entry.setText("10")  # 默认值 10
        # validator = QRegularExpressionValidator(QRegularExpression(r"\d+"))
        # self.repeat_entry.setValidator(validator)
        # self.repeat_entry.editingFinished.connect(self.validate_repeat_interval)
        # self.repeat_entry.setContextMenuPolicy(Qt.NoContextMenu)  # 禁用右键菜单
        self.switches_layout.addWidget(self.repeat_entry, stretch=1)

        self.ms_label = QLabel("ms")
        self.switches_layout.addWidget(self.ms_label, stretch=0)

        main_layout.addWidget(switches_widget)

        # 使用 QSplitter 分隔第三排和第四排
        splitter = QSplitter(Qt.Vertical)
        main_layout.addWidget(splitter, stretch=1)  # 确保 Splitter 占满剩余空间

        # 第三排：显示区，横向铺满
        recv_widget = QWidget()
        recv_widget.setObjectName("panel")
        self.recv_layout = QVBoxLayout(recv_widget)
        self.recv_layout.setContentsMargins(5, 5, 5, 5)
        self.recv_text = QTextEdit()
        self.recv_text.setObjectName("recvText")
        self.recv_text.setReadOnly(True)
        self.recv_text.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)  # 纵向扩展
        self.recv_text.setContextMenuPolicy(Qt.NoContextMenu)  # 禁用右键菜单
        self.recv_layout.addWidget(self.recv_text)

        splitter.addWidget(recv_widget)

        # 第四排：Log 区，横向铺满
        debug_widget = QWidget()
        debug_widget.setObjectName("panel")
        self.debug_layout = QVBoxLayout(debug_widget)
        self.debug_layout.setContentsMargins(5, 5, 5, 5)
        self.debug_text = QTextEdit()
        self.debug_text.setReadOnly(True)
        self.debug_text.setHtml("")
        self.debug_text.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)  # 纵向扩展
        self.debug_text.setContextMenuPolicy(Qt.NoContextMenu)  # 禁用右键菜单
        self.debug_layout.addWidget(self.debug_text)

        splitter.addWidget(debug_widget)

        # 设置 Splitter 初始占比：上70%，下30%
        splitter.setStretchFactor(0, 7)  # 第三排占比 70%
        splitter.setStretchFactor(1, 3)  # 第四排占比 30%
        splitter.setHandleWidth(5)  # 增加分隔条宽度，便于操作

        # 第五排：发送区，输入框跟随缩放，按钮固定宽度
        bottom_widget = QWidget()
        bottom_widget.setObjectName("panel")
        self.bottom_layout = QHBoxLayout(bottom_widget)
        self.bottom_layout.setContentsMargins(5, 5, 5, 5)
        self.bottom_layout.setSpacing(10)

        # CEC 特定控件
        self.initiator_combo = QComboBox()
        self.initiator_combo.addItems(CEC_LOGIC_ADDRESSES.values())
        self.initiator_combo.setFixedWidth(150)
        self.initiator_combo.setObjectName("initiatorCombo")
        self.initiator_combo.setVisible(False)
        self.bottom_layout.addWidget(self.initiator_combo)

        self.follower_combo = QComboBox()
        self.follower_combo.addItems(CEC_LOGIC_ADDRESSES.values())
        self.follower_combo.setFixedWidth(150)
        self.follower_combo.setObjectName("followerCombo")
        self.follower_combo.setVisible(False)
        self.bottom_layout.addWidget(self.follower_combo)

        self.opcode_combo = QComboBox()
        self.opcode_combo.addItems(CEC_OPCODES.values())
        self.opcode_combo.setFixedWidth(200)
        self.opcode_combo.setObjectName("opcodeCombo")
        self.opcode_combo.setVisible(False)
        self.bottom_layout.addWidget(self.opcode_combo)

        self.send_entry = QLineEdit()
        self.send_entry.setPlaceholderText("Enter data to send")
        self.bottom_layout.addWidget(self.send_entry, stretch=1)
        self.send_btn = QPushButton("Send")
        self.send_btn.setObjectName("sndButton")
        self.send_btn.setFixedWidth(80)
        self.bottom_layout.addWidget(self.send_btn)

        self.clear_send_btn = QPushButton("Clear")
        self.clear_send_btn.setObjectName("clrTxButton")
        self.clear_send_btn.setFixedWidth(80)
        self.bottom_layout.addWidget(self.clear_send_btn)

        main_layout.addWidget(bottom_widget)

        # 第六排：状态栏
        status_widget = QWidget()
        status_widget.setObjectName("panel")
        status_widget.setFixedHeight(30)  # 固定高度，避免占用过多空间
        self.status_layout = QHBoxLayout(status_widget)
        self.status_layout.setContentsMargins(5, 5, 5, 5)
        self.status_layout.setSpacing(10)

        # 左侧：连接状态、接收总数、发送总数
        self.connect_status_label = QLabel("Disconnected")
        self.connect_status_label.setObjectName("connectStatusLabel")
        self.status_layout.addWidget(self.connect_status_label)

        self.status_layout.addStretch()

        self.rx_total_label = QLabel("Rx: 0")
        self.status_layout.addWidget(self.rx_total_label)

        self.tx_total_label = QLabel("Tx: 0")
        self.status_layout.addWidget(self.tx_total_label)

        # 中间添加 stretch，确保左右分布
        self.status_layout.addStretch()

        # 右侧：版本号、版权信息
        self.version_label = QLabel(f"Version: {VERSION}")
        self.status_layout.addWidget(self.version_label)

        self.copyright_label = QLabel(COPYRIGHT)
        self.status_layout.addWidget(self.copyright_label)

        main_layout.addWidget(status_widget)

        # 启用鼠标跟踪
        self.setMouseTracking(True)
        central_widget.setMouseTracking(True)

        # 设置窗口图标
        icon_path = resource_path("app_icon.ico")
        if os.path.exists(icon_path):
            self.setWindowIcon(QIcon(icon_path))
        else:
            # 尝试加载备用图标或使用内置图标
            backup_icon_path = resource_path(os.path.join("icons", "app_icon.ico"))
            if os.path.exists(backup_icon_path):
                self.setWindowIcon(QIcon(backup_icon_path))
            else:
                # 如果没有找到图标文件，可以尝试使用 PyQt 内置图标
                self.setWindowIcon(QIcon.fromTheme("application-x-executable"))

        # 更新标题栏图标
        if not self.windowIcon().isNull():
            pixmap = self.windowIcon().pixmap(QSize(20, 20))
            self.title_bar.title_icon.setPixmap(pixmap)
            self.title_bar.title_icon.setVisible(True)
        else:
            self.title_bar.title_icon.setVisible(False)

    def update_connect_status(self, connected: bool):
        """更新连接状态显示"""
        self.connect_status_label.setText("Connected" if connected else "Disconnected")
        self.connect_status_label.setProperty("connected", "true" if connected else "false")
        self.connect_status_label.style().unpolish(self.connect_status_label)
        self.connect_status_label.style().polish(self.connect_status_label)

    def update_rx_total(self, total: int):
        """更新接收总数显示"""
        self.rx_total_label.setText(f"Rx: {total}")

    def update_tx_total(self, total: int):
        """更新发送总数显示"""
        self.tx_total_label.setText(f"Tx: {total}")

    def mousePressEvent(self, event):
        """处理鼠标按下事件，用于缩放"""
        if self.isMaximized():
            return
        if event.button() == Qt.MouseButton.LeftButton:
            self._resize_direction = self.get_resize_direction(event.position().toPoint())
            if self._resize_direction:
                self._resizing = True
                self.setCursor(self.get_resize_cursor(self._resize_direction))
                event.accept()
            super().mousePressEvent(event)

    def mouseMoveEvent(self, event):
        """处理鼠标移动事件，用于缩放和光标变化"""
        if self.isMaximized():  # 新增：最大化时禁用光标变化
            self.unsetCursor()
            return
        
        pos = event.position().toPoint()

        if self._resizing and self._resize_direction:
            rect = self.geometry()
            global_pos = event.globalPosition().toPoint()

            if 'left' in self._resize_direction:
                rect.setLeft(global_pos.x())
            if 'right' in self._resize_direction:
                rect.setRight(global_pos.x())
            if 'top' in self._resize_direction:
                rect.setTop(global_pos.y())
            if 'bottom' in self._resize_direction:
                rect.setBottom(global_pos.y())

            if rect.width() >= self.minimumWidth() and rect.height() >= self.minimumHeight():
                self.setGeometry(rect)

            event.accept()
            return

        # 更新光标形状
        direction = self.get_resize_direction(pos)
        if direction:
            self.setCursor(self.get_resize_cursor(direction))
        else:
            self.unsetCursor()

        super().mouseMoveEvent(event)

    def mouseReleaseEvent(self, event):
        """处理鼠标释放事件，结束缩放"""
        self._resizing = False
        self._resize_direction = None
        self.unsetCursor()
        super().mouseReleaseEvent(event)

    def get_resize_direction(self, pos):
        """根据鼠标位置确定缩放方向"""
        if self.isMaximized():  # 新增：最大化时无缩放方向
            return None
        
        rect = self.rect()
        border_width = 3

        left = pos.x() < border_width
        right = pos.x() > rect.width() - border_width
        top = pos.y() < border_width
        bottom = pos.y() > rect.height() - border_width

        if left and top:
            return 'top-left'
        elif right and top:
            return 'top-right'
        elif left and bottom:
            return 'bottom-left'
        elif right and bottom:
            return 'bottom-right'
        elif left:
            return 'left'
        elif right:
            return 'right'
        elif top:
            return 'top'
        elif bottom:
            return 'bottom'

        return None

    @staticmethod
    def get_resize_cursor(direction):
        """根据缩放方向返回对应的光标形状"""
        if direction in ('top-left', 'bottom-right'):
            return Qt.CursorShape.SizeFDiagCursor
        elif direction in ('top-right', 'bottom-left'):
            return Qt.CursorShape.SizeBDiagCursor
        elif direction in ('left', 'right'):
            return Qt.CursorShape.SizeHorCursor
        elif direction in ('top', 'bottom'):
            return Qt.CursorShape.SizeVerCursor

        return Qt.CursorShape.ArrowCursor

    def append_debug(self, message):
        """将 Debug 信息追加到 debug_text"""
        self.debug_text.append(message)
        self.debug_text.ensureCursorVisible()

    def save_text(self):
        """保存显示区数据到文件"""
        content = self.recv_text.toPlainText()
        if content:
            filename = f"serial_data_{datetime.datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
            with open(filename, 'w', encoding='utf-8') as f:
                f.write(content)
            logger.info(f"Data saved to {filename}")
        else:
            logger.info("No data to save")

    # def validate_repeat_interval(self):
    #     """验证 repeat_entry 的输入值，确保最小为 10"""
    #     text = self.repeat_entry.text()
    #     if text and int(text) < 10:
    #         self.repeat_entry.setText("10")
    #         logger.info("Repeat interval adjusted to minimum value: 10ms")

    def get_selected_terminator(self) -> bytes:
        """获取当前选择的结束符"""
        terminator_map = {
            '-': b'',
            '\\r': b'\r',
            '\\n': b'\n',
            '\\r\\n': b'\r\n',
            '\\n\\r': b'\n\r'
        }
        return terminator_map.get(self.send_end_combo.currentText(), b'')

    @staticmethod
    def setup_combo_logging(combo: QComboBox, control_name: str):
        """为下拉框配置选项变更日志"""
        combo.currentTextChanged.connect(
            lambda text: logger.info(f"[{control_name}] {text}")
        )

    # 缩放相关变量
    _resizing = False
    _resize_direction = None