import { <PERSON><PERSON>, ComboBox, <PERSON>Box, LineEdit, TextEdit, HorizontalBox, VerticalBox, ScrollView } from "std-widgets.slint";

// 自定义样式组件
component CustomButton inherits <PERSON><PERSON> {
    min-width: 80px;
    min-height: 32px;
}

component CustomComboBox inherits ComboBox {
    min-width: 150px;
    min-height: 32px;
}

component CustomLineEdit inherits LineEdit {
    min-height: 32px;
}

component CustomTextEdit inherits TextEdit {
    // 使用默认样式
}

component TitleBar inherits Rectangle {
    property <string> title: "Serial Communication Tool";

    callback minimize();
    callback maximize();
    callback close();

    background: #2c3e50;
    height: 40px;

    HorizontalBox {
        padding: 8px;
        spacing: 8px;

        // 标题
        Text {
            text: root.title;
            color: #ffffff;
            vertical-alignment: center;
        }

        Rectangle { } // 占位符，推送按钮到右侧

        // 窗口控制按钮
        HorizontalBox {
            spacing: 4px;

            <PERSON><PERSON> {
                text: "−";
                width: 32px;
                height: 24px;
                clicked => { root.minimize(); }
            }

            <PERSON><PERSON> {
                text: "□";
                width: 32px;
                height: 24px;
                clicked => { root.maximize(); }
            }

            <PERSON><PERSON> {
                text: "×";
                width: 32px;
                height: 24px;
                clicked => { root.close(); }
            }
        }
    }
}

export component SerialGUI inherits Window {
    title: "Serial Communication Tool";
    width: 1200px;
    height: 800px;
    min-width: 1200px;
    min-height: 800px;
    
    // 属性定义
    property <[string]> port-list: [];
    property <[string]> baud-rates: ["19200", "38400", "57600", "115200", "128000", "256000", "460800", "921600"];
    property <[string]> send-ends: ["-", "\\r", "\\n", "\\r\\n", "\\n\\r"];
    property <[string]> cec-initiators: [];
    property <[string]> cec-followers: [];
    property <[string]> cec-opcodes: [];
    
    property <string> selected-port;
    property <string> selected-baud: "115200";
    property <string> selected-send-end: "-";
    property <string> selected-initiator;
    property <string> selected-follower;
    property <string> selected-opcode;
    
    property <bool> connected: false;
    property <bool> cec-debug: false;
    property <bool> rx-hex: false;
    property <bool> timestamp: true;
    property <bool> tx-hex: false;
    property <bool> repeat-enabled: false;
    
    property <string> repeat-interval: "10";
    property <string> send-text;
    property <string> recv-text;
    property <string> debug-text;
    
    property <string> connect-status: "Disconnected";
    property <int> rx-total: 0;
    property <int> tx-total: 0;
    property <string> version: "1.0.0";
    property <string> copyright: "© 2024";
    
    // 回调函数
    callback connect-clicked();
    callback send-clicked();
    callback clear-clicked();
    callback save-clicked();
    callback clear-send-clicked();
    
    callback cec-debug-changed(bool);
    callback rx-hex-changed(bool);
    callback timestamp-changed(bool);
    callback tx-hex-changed(bool);
    callback repeat-changed(bool);
    
    callback port-changed(string);
    callback baud-changed(string);
    callback send-end-changed(string);
    callback initiator-changed(string);
    callback follower-changed(string);
    callback opcode-changed(string);
    
    callback send-text-changed(string);
    callback repeat-interval-changed(string);
    
    VerticalBox {
        padding: 0px;
        spacing: 0px;
        
        // 标题栏
        title-bar := TitleBar {
            minimize => { /* 最小化窗口 */ }
            maximize => { /* 最大化窗口 */ }
            close => { /* 关闭窗口 */ }
        }
        
        // 主内容区域
        VerticalBox {
            padding: 5px;
            spacing: 5px;
            
            // 第一排：连接控制
            Rectangle {
                background: #ecf0f1;
                border-radius: 4px;
                height: 50px;
                
                HorizontalBox {
                    padding: 5px;
                    spacing: 10px;
                    
                    port-combo := CustomComboBox {
                        model: root.port-list;
                        current-value: root.selected-port;
                        selected => { root.port-changed(self.current-value); }
                    }
                    
                    baud-combo := CustomComboBox {
                        model: root.baud-rates;
                        current-value: root.selected-baud;
                        selected => { root.baud-changed(self.current-value); }
                    }
                    
                    connect-btn := CustomButton {
                        text: root.connected ? "Disconnect" : "Connect";
                        clicked => { root.connect-clicked(); }
                    }
                    
                    clear-btn := CustomButton {
                        text: "Clear";
                        clicked => { root.clear-clicked(); }
                    }
                    
                    save-btn := CustomButton {
                        text: "Save";
                        clicked => { root.save-clicked(); }
                    }
                }
            }
            
            // 第二排：开关控制
            Rectangle {
                background: #ecf0f1;
                border-radius: 4px;
                height: 50px;
                
                HorizontalBox {
                    padding: 5px;
                    spacing: 10px;
                    
                    CheckBox {
                        text: "CEC Debug";
                        checked: root.cec-debug;
                        toggled => { root.cec-debug-changed(self.checked); }
                    }
                    
                    CheckBox {
                        text: "Rx Hex";
                        checked: root.rx-hex;
                        toggled => { root.rx-hex-changed(self.checked); }
                    }
                    
                    CheckBox {
                        text: "Timestamp";
                        checked: root.timestamp;
                        toggled => { root.timestamp-changed(self.checked); }
                    }
                    
                    CheckBox {
                        text: "Tx Hex";
                        checked: root.tx-hex;
                        toggled => { root.tx-hex-changed(self.checked); }
                    }
                    
                    CustomComboBox {
                        width: 100px;
                        model: root.send-ends;
                        current-value: root.selected-send-end;
                        selected => { root.send-end-changed(self.current-value); }
                    }
                    
                    CheckBox {
                        text: "Repeat";
                        checked: root.repeat-enabled;
                        toggled => { root.repeat-changed(self.checked); }
                    }
                    
                    CustomLineEdit {
                        width: 60px;
                        text: root.repeat-interval;
                        edited => { root.repeat-interval-changed(self.text); }
                    }
                    
                    Text {
                        text: "ms";
                        vertical-alignment: center;
                    }
                }
            }

            // 数据显示区域和日志区域
            VerticalBox {

                // 接收数据显示区
                Rectangle {
                    background: #ecf0f1;
                    border-radius: 4px;
                    min-height: 200px;

                    VerticalBox {
                        padding: 5px;

                        recv-text-edit := CustomTextEdit {
                            text: root.recv-text;
                            read-only: true;
                        }
                    }
                }

                // 调试日志区
                Rectangle {
                    background: #ecf0f1;
                    border-radius: 4px;
                    min-height: 100px;

                    VerticalBox {
                        padding: 5px;

                        debug-text-edit := CustomTextEdit {
                            text: root.debug-text;
                            read-only: true;
                        }
                    }
                }
            }

            // CEC控制区域 (条件显示)
            if root.cec-debug: Rectangle {
                background: #ecf0f1;
                border-radius: 4px;
                height: 50px;

                HorizontalBox {
                    padding: 5px;
                    spacing: 10px;

                    CustomComboBox {
                        width: 150px;
                        model: root.cec-initiators;
                        current-value: root.selected-initiator;
                        selected => { root.initiator-changed(self.current-value); }
                    }

                    CustomComboBox {
                        width: 150px;
                        model: root.cec-followers;
                        current-value: root.selected-follower;
                        selected => { root.follower-changed(self.current-value); }
                    }

                    CustomComboBox {
                        width: 200px;
                        model: root.cec-opcodes;
                        current-value: root.selected-opcode;
                        selected => { root.opcode-changed(self.current-value); }
                    }
                }
            }

            // 发送区域
            Rectangle {
                background: #ecf0f1;
                border-radius: 4px;
                height: 50px;

                HorizontalBox {
                    padding: 5px;
                    spacing: 10px;

                    send-input := CustomLineEdit {
                        placeholder-text: "Enter data to send";
                        text: root.send-text;
                        edited => { root.send-text-changed(self.text); }
                    }

                    send-btn := CustomButton {
                        text: "Send";
                        clicked => { root.send-clicked(); }
                    }

                    clear-send-btn := CustomButton {
                        text: "Clear";
                        clicked => { root.clear-send-clicked(); }
                    }
                }
            }

            // 状态栏
            Rectangle {
                background: #34495e;
                height: 30px;
                border-radius: 4px;

                HorizontalBox {
                    padding: 5px;
                    spacing: 10px;

                    Text {
                        text: root.connect-status;
                        color: root.connected ? #2ecc71 : #e74c3c;
                        vertical-alignment: center;
                    }

                    Rectangle { } // 占位符

                    Text {
                        text: "Rx: " + root.rx-total;
                        color: #ffffff;
                        vertical-alignment: center;
                    }

                    Text {
                        text: "Tx: " + root.tx-total;
                        color: #ffffff;
                        vertical-alignment: center;
                    }

                    Rectangle { } // 占位符

                    Text {
                        text: "Version: " + root.version;
                        color: #bdc3c7;
                        vertical-alignment: center;
                    }

                    Text {
                        text: root.copyright;
                        color: #bdc3c7;
                        vertical-alignment: center;
                    }
                }
            }
        }
    }
}
