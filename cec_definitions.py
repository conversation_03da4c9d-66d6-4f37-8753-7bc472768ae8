from collections import namedtuple

# CEC 相关定义
CEC_LOGIC_ADDRESSES = {
    0x0: "TV",
    0x1: "Recorder 1",
    0x2: "Recorder 2",
    0x3: "Tuner 1",
    0x4: "Playback 1",
    0x5: "Audio System",
    0x6: "Tuner 2",
    0x7: "Tuner 3",
    0x8: "Playback 2",
    0x9: "Recorder 3",
    0xA: "Tuner 4",
    0xB: "Playback 3",
    0xC: "Reserved 1",
    0xD: "Reserved 2",
    0xE: "Free use",
    0xF: "Broadcast",
}

CEC_OPCODES = {
    0x00: "Feature Abort",
    0x04: "Image View On",
    0x05: "Tuner Step Increment",
    0x06: "Tuner Step Decrement",
    0x07: "Tuner Device Status",
    0x08: "Give Tuner Device Status",
    0x09: "Record On",
    0x0A: "Record Status",
    0x0B: "Record Off",
    0x0D: "Text View On",
    0x0F: "Record TV Screen",
    0x1A: "Give Deck Status",
    0x1B: "Deck Status",
    0x32: "Set Menu Language",
    0x33: "Clear Analogue Timer",
    0x34: "Set Analogue Timer",
    0x35: "Timer Status",
    0x36: "Standby",
    0x41: "Play",
    0x42: "Deck Control",
    0x43: "Timer Cleared Status",
    0x44: "User Control Pressed",
    0x45: "User Control Released",
    0x46: "Give OSD Name",
    0x47: "Set OSD Name",
    0x64: "Set OSD String",
    0x67: "Set Timer Program Title",
    0x70: "System Audio Mode Request",
    0x71: "Give Audio Status",
    0x72: "Set System Audio Mode",
    0x7A: "Report Audio Status",
    0x7D: "Give System Audio Mode Status",
    0x7E: "System Audio Mode Status",
    0x80: "Routing Change",
    0x81: "Routing Information",
    0x82: "Active Source",
    0x83: "Give Physical Address",
    0x84: "Report Physical Address",
    0x85: "Request Active Source",
    0x86: "Set Stream Path",
    0x87: "Device Vendor ID",
    0x89: "Vendor Command",
    0x8A: "Vendor Remote Button Down",
    0x8B: "Vendor Remote Button Up",
    0x8C: "Give Device Vendor ID",
    0x8D: "Menu Request",
    0x8E: "Menu Status",
    0x8F: "Give Device Power Status",
    0x90: "Report Power Status",
    0x91: "Get Menu Language",
    0x92: "Select Analogue Service",
    0x93: "Select Digital Service",
    0x97: "Set Digital Timer",
    0x99: "Clear Digital Timer",
    0x9A: "Set Audio Rate",
    0x9D: "Inactive Source",
    0x9E: "CEC Version",
    0x9F: "Get CEC Version",
    0xA0: "Vendor Command With ID",
    0xA1: "Clear External Timer",
    0xA2: "Set External Timer",
    0xA3: "Report Short Audio Descriptor",
    0xA4: "Request Short Audio Descriptor",
    0xC0: "Initiate ARC",
    0xC1: "Report ARC Initiated",
    0xC2: "Report ARC Terminated",
    0xC3: "Request ARC Initiation",
    0xC4: "Request ARC Termination",
    0xC5: "Terminate ARC",
}

OpcodeInfo = namedtuple('OpcodeInfo', ['length', 'fixed', 'lengths', 'length_range'])
OPCODE_PARAMETER_LENGTHS = {
    0x00: OpcodeInfo(2, True, None, None),
    0x04: OpcodeInfo(0, True, None, None),
    0x05: OpcodeInfo(0, True, None, None),
    0x06: OpcodeInfo(0, True, None, None),
    0x07: OpcodeInfo(None, True, [5, 8], None),
    0x08: OpcodeInfo(1, True, None, None),
    0x09: OpcodeInfo(None, False, None, (1, 8)),
    0x0A: OpcodeInfo(1, True, None, None),
    0x0B: OpcodeInfo(0, True, None, None),
    0x0D: OpcodeInfo(0, True, None, None),
    0x0F: OpcodeInfo(0, True, None, None),
    0x1A: OpcodeInfo(1, True, None, None),
    0x1B: OpcodeInfo(1, True, None, None),
    0x32: OpcodeInfo(3, True, None, None),
    0x33: OpcodeInfo(11, True, None, None),
    0x34: OpcodeInfo(11, True, None, None),
    0x35: OpcodeInfo(None, True, [1, 3], None),
    0x36: OpcodeInfo(0, True, None, None),
    0x41: OpcodeInfo(1, True, None, None),
    0x42: OpcodeInfo(1, True, None, None),
    0x43: OpcodeInfo(1, True, None, None),
    0x44: OpcodeInfo(1, True, None, None),
    0x45: OpcodeInfo(0, True, None, None),
    0x46: OpcodeInfo(0, True, None, None),
    0x47: OpcodeInfo(None, False, None, (1, 14)),
    0x64: OpcodeInfo(None, False, None, (2, 14)),
    0x67: OpcodeInfo(None, False, None, (1, 14)),
    0x70: OpcodeInfo(None, True, [0, 2], None),
    0x71: OpcodeInfo(0, True, None, None),
    0x72: OpcodeInfo(1, True, None, None),
    0x7A: OpcodeInfo(1, True, None, None),
    0x7D: OpcodeInfo(0, True, None, None),
    0x7E: OpcodeInfo(1, True, None, None),
    0x80: OpcodeInfo(4, True, None, None),
    0x81: OpcodeInfo(2, True, None, None),
    0x82: OpcodeInfo(2, True, None, None),
    0x83: OpcodeInfo(0, True, None, None),
    0x84: OpcodeInfo(3, True, None, None),
    0x85: OpcodeInfo(0, True, None, None),
    0x86: OpcodeInfo(2, True, None, None),
    0x87: OpcodeInfo(3, True, None, None),
    0x89: OpcodeInfo(None, False, None, (1, 13)),
    0x8A: OpcodeInfo(None, False, None, (1, 14)),
    0x8B: OpcodeInfo(0, True, None, None),
    0x8C: OpcodeInfo(0, True, None, None),
    0x8D: OpcodeInfo(1, True, None, None),
    0x8E: OpcodeInfo(1, True, None, None),
    0x8F: OpcodeInfo(0, True, None, None),
    0x90: OpcodeInfo(1, True, None, None),
    0x91: OpcodeInfo(0, True, None, None),
    0x92: OpcodeInfo(4, True, None, None),
    0x93: OpcodeInfo(7, True, None, None),
    0x97: OpcodeInfo(14, True, None, None),
    0x99: OpcodeInfo(14, True, None, None),
    0x9A: OpcodeInfo(1, True, None, None),
    0x9D: OpcodeInfo(2, True, None, None),
    0x9E: OpcodeInfo(1, True, None, None),
    0x9F: OpcodeInfo(0, True, None, None),
    0xA0: OpcodeInfo(None, False, None, (4, 13)),
    0xA1: OpcodeInfo(None, True, [9, 10], None),
    0xA2: OpcodeInfo(None, True, [9, 10], None),
    0xA3: OpcodeInfo(None, True, [3, 6, 9, 12], None),
    0xA4: OpcodeInfo(None, False, None, (1, 4)),
    0xC0: OpcodeInfo(0, True, None, None),
    0xC1: OpcodeInfo(0, True, None, None),
    0xC2: OpcodeInfo(0, True, None, None),
    0xC3: OpcodeInfo(0, True, None, None),
    0xC4: OpcodeInfo(0, True, None, None),
    0xC5: OpcodeInfo(0, True, None, None),
}
