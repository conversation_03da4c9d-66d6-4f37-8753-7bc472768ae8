import logging
from cec_definitions import CEC_LOGIC_ADDRESSES, CEC_OPCODES

logger = logging.getLogger('CEC Parser')

class CECParser:
    @staticmethod
    def parse_frame(frame):
        """解析CEC数据帧"""
        if len(frame) < 5:  # 最小有效帧长度(AA 88 Len Header Data Checksum)
            logger.debug(f"Frame too short ({len(frame)} bytes), minimum 5 bytes required")
            return None
            
        try:
            # 解析CEC协议部分(跳过AA 88头)
            cec_data = frame[3:-2]  # 跳过协议头(AA 88 Len)和状态,校验和
            # logger.debug(f"Raw frame: {' '.join(f'{b:02X}' for b in frame)}")
            
            if not cec_data:
                logger.debug("No CEC data after removing header and checksum")
                return None
                
            # 解析CEC头
            header = cec_data[0]
            initiator = (header >> 4) & 0x0F
            follower = header & 0x0F
            # logger.debug(f"Header: {header:02X}, Initiator: {initiator:02X}, Follower: {follower:02X}")
            
            # 获取描述信息
            initiator_desc = CEC_LOGIC_ADDRESSES.get(initiator, f"Unknown({initiator:02X})")
            follower_desc = CEC_LOGIC_ADDRESSES.get(follower, f"Unknown({follower:02X})")
            # logger.debug(f"Initiator: {initiator_desc}, Follower: {follower_desc}")

            parsed_bytes = [f"{header:02X}({initiator_desc} -> {follower_desc})"]

            if len(cec_data) > 1:
                opcode = cec_data[1]
                opcode_desc = CEC_OPCODES.get(opcode, f"Unknown({opcode:02X})")
                # logger.debug(f"Opcode: {opcode:02X} ({opcode_desc})")
                parsed_bytes.append(f"{opcode:02X}({opcode_desc})")
            
            # 记录参数信息
            params = cec_data[2:] if len(cec_data) > 2 else []
            # if params:
            #     logger.debug(f"Parameters: {' '.join(f'{b:02X}' for b in params)}")
            
            for i, byte in enumerate(params):
                parsed_bytes.append(f"{byte:02X}")
            
            result = {
                'raw': ' '.join(f'{b:02X}' for b in frame),
                'summary': ' '.join(parsed_bytes)
            }
            #logger.debug(f"Parsed result: {result}")
            return result
            
        except Exception as e:
            logger.error(f"CEC parse error: {e}")
            return None